{"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 80, "tabWidth": 2, "useTabs": false, "quoteProps": "as-needed", "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "endOfLine": "lf", "embeddedLanguageFormatting": "auto", "htmlWhitespaceSensitivity": "css", "insertPragma": false, "jsxSingleQuote": true, "proseWrap": "preserve", "requirePragma": false, "vueIndentScriptAndStyle": false, "plugins": ["prettier-plugin-tailwindcss"], "tailwindConfig": "./config/tailwind.config.js", "tailwindFunctions": ["clsx", "cn", "cva"], "overrides": [{"files": "*.html", "options": {"parser": "html", "printWidth": 120, "htmlWhitespaceSensitivity": "ignore"}}, {"files": "*.css", "options": {"parser": "css", "printWidth": 100}}, {"files": "*.json", "options": {"parser": "json", "printWidth": 100, "tabWidth": 2}}, {"files": "*.md", "options": {"parser": "markdown", "printWidth": 80, "proseWrap": "always", "tabWidth": 2}}, {"files": "*.js", "options": {"parser": "babel", "printWidth": 80, "tabWidth": 2}}]}