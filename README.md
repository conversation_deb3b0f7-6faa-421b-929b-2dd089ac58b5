# <PERSON> - Personal Portfolio

A modern, responsive personal portfolio website built with **HTML5**, **Tailwind CSS v4.1.11**, and **Vanilla JavaScript**. This portfolio showcases professional web development skills with a focus on performance, security, and user experience.

## 🚀 Features

### ✨ Design & User Experience
- **Responsive Design** - Optimized for all devices and screen sizes
- **Modern UI/UX** - Clean, professional, and intuitive interface
- **Smooth Animations** - Subtle animations and transitions
- **Accessibility** - WCAG compliant and screen reader friendly
- **Cross-browser Compatible** - Works seamlessly across all modern browsers

### ⚡ Performance
- **Optimized Loading** - Fast page load times with optimized assets
- **Lazy Loading** - Images and content loaded on demand
- **Minified Assets** - Compressed CSS, JavaScript, and HTML
- **Efficient Caching** - Smart caching strategies for better performance

### 🔒 Security
- **Content Protection** - Disabled right-click, inspect element, and copy-paste
- **Code Obfuscation** - Protected source code from easy access
- **XSS Prevention** - Sanitized inputs and secure coding practices
- **HTTPS Ready** - Secure connection support

### 🛠️ Technical Features
- **Modern JavaScript** - ES6+ features and best practices
- **Component-based Architecture** - Modular and maintainable code
- **Data-driven Content** - JSON-based content management
- **SEO Optimized** - Meta tags, structured data, and sitemap
- **Progressive Enhancement** - Works without JavaScript

## 🏗️ Project Structure

```
personal-portfolio/
├── 📁 assets/                    # Static assets
│   ├── 📁 icons/
│   │   ├── 📁 social/           # Social media icons
│   │   ├── 📁 tech/             # Technology stack icons
│   │   └── 📁 ui/               # UI interface icons
│   ├── 📁 images/
│   │   ├── 📁 profile/          # Profile photos
│   │   ├── 📁 projects/         # Project screenshots
│   │   ├── 📁 certificates/     # Achievement certificates
│   │   └── 📁 gallery/          # Additional images
│   └── 📁 files/                # Documents (resume, CV)
├── 📁 src/                      # Source code
│   ├── 📁 css/
│   │   ├── input.css            # Main Tailwind input
│   │   ├── components.css       # Component styles
│   │   └── custom.css           # Custom utilities
│   ├── 📁 js/
│   │   ├── main.js              # Main application
│   │   ├── components.js        # Component system
│   │   └── utils.js             # Utility functions
│   ├── 📁 components/           # HTML components
│   │   ├── header.html
│   │   ├── footer.html
│   │   └── 📁 sections/         # Page sections
│   └── 📁 data/                 # Content data
│       ├── profile.json         # Personal information
│       ├── skills.json          # Technical skills
│       ├── projects.json        # Portfolio projects
│       └── social.json          # Social media links
├── 📁 dist/                     # Build output
├── 📁 config/                   # Configuration files
├── 📁 tools/                    # Development tools
└── 📁 docs/                     # Documentation
```

## 🚀 Quick Start

### Prerequisites
- **Node.js** (v16 or higher)
- **npm** or **yarn**
- **Git**

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/syaamil/personal-portfolio.git
   cd personal-portfolio
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Open in browser**
   ```
   http://localhost:3000
   ```

## 📜 Available Scripts

### Development
```bash
npm run dev          # Start development server with hot reload
npm run build:css    # Build CSS with Tailwind CLI
npm run watch:css    # Watch CSS changes
```

### Production
```bash
npm run build        # Build for production
npm run optimize     # Optimize assets
npm run preview      # Preview production build
```

### Utilities
```bash
npm run clean        # Clean build directories
npm run lint         # Lint code
npm run format       # Format code with Prettier
```

## 🎨 Customization

### Personal Information
Update your personal information in `src/data/profile.json`:

```json
{
  "personal": {
    "name": "Your Name",
    "title": "Your Title",
    "email": "<EMAIL>",
    "profileImage": "assets/images/profile/your-photo.jpg"
  }
}
```

### Skills & Technologies
Add your skills in `src/data/skills.json`:

```json
{
  "technical": [
    {
      "category": "Frontend Development",
      "skills": [
        {
          "name": "HTML5",
          "level": 95,
          "icon": "assets/icons/tech/html5.svg"
        }
      ]
    }
  ]
}
```

### Projects
Showcase your projects in `src/data/projects.json`:

```json
{
  "featured": [
    {
      "title": "Project Name",
      "description": "Project description",
      "technologies": ["HTML5", "CSS3", "JavaScript"],
      "liveUrl": "https://project-demo.com",
      "githubUrl": "https://github.com/username/project"
    }
  ]
}
```

### Styling
Customize the design in `config/tailwind.config.js`:

```javascript
export default {
  theme: {
    extend: {
      colors: {
        primary: {
          500: '#your-primary-color',
        }
      }
    }
  }
}
```

## 🔧 Configuration

### Site Configuration
Update site settings in `config/site.config.js`:

```javascript
export default {
  site: {
    name: "Your Name",
    url: "https://yourwebsite.com",
    description: "Your description"
  }
}
```

### Build Configuration
Modify build settings in `config/build.config.js` for advanced customization.

## 📱 Browser Support

- **Chrome** 60+
- **Firefox** 60+
- **Safari** 11+
- **Edge** 18+
- **Mobile browsers** (iOS Safari, Chrome Mobile)

## 🚀 Deployment

### GitHub Pages
1. Build the project: `npm run build`
2. Push to GitHub
3. Enable GitHub Pages in repository settings

### Netlify
1. Connect your GitHub repository
2. Set build command: `npm run build`
3. Set publish directory: `dist`

### Vercel
1. Import your GitHub repository
2. Vercel will auto-detect settings
3. Deploy with one click

## 📈 Performance

- **Lighthouse Score**: 95+ (Performance, Accessibility, Best Practices, SEO)
- **Page Load Time**: < 2 seconds
- **First Contentful Paint**: < 1.5 seconds
- **Cumulative Layout Shift**: < 0.1

## 🔒 Security Features

- **Content Security Policy** (CSP) headers
- **XSS Protection** with input sanitization
- **Clickjacking Protection** with X-Frame-Options
- **HTTPS Enforcement** ready
- **Source Code Protection** with obfuscation

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👨‍💻 Author

**Muhammad Syaamil Muzhaffar**
- Website: [syaamil.dev](https://syaamil.dev)
- GitHub: [@syaamil](https://github.com/syaamil)
- Instagram: [@syaamil.dev](https://instagram.com/syaamil.dev)
- Medium: [@syaamil](https://medium.com/@syaamil)

## 🙏 Acknowledgments

- **Tailwind CSS** for the amazing utility-first CSS framework
- **Anthropic Claude** for development assistance
- **Open Source Community** for inspiration and resources

---

⭐ **Star this repository if you found it helpful!**
