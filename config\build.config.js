/**
 * Build Configuration for Personal Portfolio
 * Author: <PERSON>
 * Description: Build process configuration and optimization settings
 */

import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export default {
  // Project paths
  paths: {
    root: path.resolve(__dirname, '..'),
    src: path.resolve(__dirname, '../src'),
    dist: path.resolve(__dirname, '../dist'),
    assets: path.resolve(__dirname, '../assets'),
    config: path.resolve(__dirname, '../config'),
    tools: path.resolve(__dirname, '../tools'),
    public: path.resolve(__dirname, '../public')
  },

  // Entry points
  entry: {
    main: './src/js/main.js',
    utils: './src/js/utils.js',
    components: './src/js/components.js'
  },

  // Output configuration
  output: {
    path: path.resolve(__dirname, '../dist'),
    filename: 'js/[name].js',
    assetModuleFilename: 'assets/[name][ext]',
    clean: true
  },

  // CSS Configuration
  css: {
    // Input files
    input: './src/css/input.css',
    
    // Output file
    output: './dist/css/output.css',
    
    // PostCSS plugins
    plugins: [
      'tailwindcss',
      'autoprefixer'
    ],
    
    // Minification
    minify: true,
    
    // Source maps
    sourcemap: false,
    
    // Purge unused CSS
    purge: true,
    
    // Watch mode
    watch: [
      './src/**/*.{html,js}',
      './index.html'
    ]
  },

  // JavaScript Configuration
  javascript: {
    // Target browsers
    target: ['es2020', 'chrome60', 'firefox60', 'safari11', 'edge18'],
    
    // Module format
    format: 'iife',
    
    // Minification
    minify: true,
    
    // Source maps
    sourcemap: false,
    
    // Tree shaking
    treeShaking: true,
    
    // Bundle splitting
    splitting: false,
    
    // External dependencies
    external: [],
    
    // Global variables
    globals: {
      'Utils': 'Utils',
      'Components': 'Components'
    }
  },

  // HTML Configuration
  html: {
    // Template file
    template: './index.html',
    
    // Output file
    output: './dist/index.html',
    
    // Minification
    minify: {
      removeComments: true,
      collapseWhitespace: true,
      removeRedundantAttributes: true,
      useShortDoctype: true,
      removeEmptyAttributes: true,
      removeStyleLinkTypeAttributes: true,
      keepClosingSlash: true,
      minifyJS: true,
      minifyCSS: true,
      minifyURLs: true
    },
    
    // Inject assets
    inject: true,
    
    // Meta tags
    meta: {
      viewport: 'width=device-width, initial-scale=1.0',
      'theme-color': '#3b82f6',
      'msapplication-TileColor': '#3b82f6'
    }
  },

  // Asset Configuration
  assets: {
    // Image optimization
    images: {
      // Formats to generate
      formats: ['webp', 'jpg', 'png'],
      
      // Quality settings
      quality: {
        webp: 85,
        jpg: 85,
        png: 90
      },
      
      // Responsive sizes
      sizes: [320, 640, 768, 1024, 1280, 1920],
      
      // Optimization
      optimize: true,
      
      // Progressive JPEG
      progressive: true
    },
    
    // Icon generation
    icons: {
      // Favicon sizes
      favicon: [16, 32, 48],
      
      // Apple touch icons
      apple: [57, 60, 72, 76, 114, 120, 144, 152, 180],
      
      // Android icons
      android: [36, 48, 72, 96, 144, 192, 512],
      
      // Windows tiles
      windows: [70, 150, 310]
    },
    
    // Font optimization
    fonts: {
      // Preload critical fonts
      preload: [
        'Inter-Regular.woff2',
        'Inter-Medium.woff2',
        'Inter-SemiBold.woff2'
      ],
      
      // Font display strategy
      display: 'swap',
      
      // Subset fonts
      subset: true
    }
  },

  // Development Configuration
  development: {
    // Development server
    server: {
      port: 3000,
      host: 'localhost',
      open: true,
      https: false,
      cors: true
    },
    
    // Hot reload
    hotReload: true,
    
    // Live reload
    liveReload: true,
    
    // Source maps
    sourcemap: true,
    
    // Watch options
    watch: {
      ignored: ['node_modules/**', 'dist/**'],
      usePolling: false
    }
  },

  // Production Configuration
  production: {
    // Minification
    minify: {
      html: true,
      css: true,
      js: true,
      images: true
    },
    
    // Compression
    compression: {
      gzip: true,
      brotli: true,
      level: 9
    },
    
    // Cache busting
    cacheBusting: true,
    
    // Bundle analysis
    analyze: false,
    
    // Performance budget
    budget: {
      maximumFileSizeToCacheInBytes: 2 * 1024 * 1024, // 2MB
      maximumBundleSize: 1 * 1024 * 1024, // 1MB
      maximumAssetSize: 500 * 1024 // 500KB
    }
  },

  // Optimization Configuration
  optimization: {
    // CSS optimization
    css: {
      // Remove unused CSS
      purgeCSS: {
        content: [
          './src/**/*.{html,js}',
          './index.html'
        ],
        safelist: [
          'fade-in',
          'slide-up',
          'scale-in',
          /^animate-/,
          /^transition-/
        ]
      },
      
      // Critical CSS
      critical: {
        inline: true,
        minify: true,
        extract: false
      }
    },
    
    // JavaScript optimization
    javascript: {
      // Dead code elimination
      deadCodeElimination: true,
      
      // Constant folding
      constantFolding: true,
      
      // Function inlining
      functionInlining: true,
      
      // Variable renaming
      variableRenaming: true
    },
    
    // Image optimization
    images: {
      // Lossless compression
      lossless: false,
      
      // Quality
      quality: 85,
      
      // Progressive
      progressive: true,
      
      // Metadata removal
      removeMetadata: true
    }
  },

  // Plugin Configuration
  plugins: {
    // Copy static files
    copy: [
      {
        from: 'assets/**/*',
        to: 'assets/'
      },
      {
        from: 'public/**/*',
        to: './'
      }
    ],
    
    // Generate sitemap
    sitemap: {
      hostname: 'https://syaamil.dev',
      routes: [
        '/',
        '/#about',
        '/#skills',
        '/#projects',
        '/#contact'
      ]
    },
    
    // Generate robots.txt
    robots: {
      host: 'https://syaamil.dev',
      sitemap: 'https://syaamil.dev/sitemap.xml',
      policy: [
        {
          userAgent: '*',
          allow: '/',
          disallow: ['/admin/', '/private/']
        }
      ]
    }
  },

  // Environment Variables
  env: {
    // Build environment
    NODE_ENV: process.env.NODE_ENV || 'development',
    
    // Site URL
    SITE_URL: process.env.SITE_URL || 'http://localhost:3000',
    
    // Analytics
    GA_TRACKING_ID: process.env.GA_TRACKING_ID || '',
    
    // Contact form
    CONTACT_FORM_ENDPOINT: process.env.CONTACT_FORM_ENDPOINT || '',
    
    // API endpoints
    API_BASE_URL: process.env.API_BASE_URL || ''
  },

  // Build hooks
  hooks: {
    // Before build
    beforeBuild: [
      'clean',
      'lint',
      'test'
    ],
    
    // After build
    afterBuild: [
      'analyze',
      'compress',
      'deploy'
    ]
  }
};
