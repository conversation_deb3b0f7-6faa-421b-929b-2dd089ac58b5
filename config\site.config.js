/**
 * Site Configuration for Personal Portfolio
 * Author: <PERSON>
 * Description: Central configuration for site metadata and settings
 */

export default {
  // Site Information
  site: {
    name: "<PERSON>",
    title: "<PERSON> - Full Stack Developer",
    description: "Personal portfolio of <PERSON>, a passionate Full Stack Developer specializing in modern web technologies including HTML5, Tailwind CSS, and Vanilla JavaScript.",
    keywords: [
      "<PERSON>",
      "Full Stack Developer",
      "Web Developer",
      "Frontend Developer",
      "HTML5",
      "CSS3",
      "Tailwind CSS",
      "JavaScript",
      "Vanilla JavaScript",
      "Node.js",
      "Portfolio",
      "Indonesia Developer"
    ],
    url: "https://syaamil.dev",
    baseUrl: "/",
    language: "en",
    locale: "en_US",
    author: "<PERSON>",
    email: "<EMAIL>",
    phone: "+62 xxx-xxxx-xxxx",
    location: "Jakarta, Indonesia",
    timezone: "Asia/Jakarta"
  },

  // SEO Configuration
  seo: {
    titleTemplate: "%s | <PERSON>",
    defaultTitle: "<PERSON>amil <PERSON>zhaffar - Full Stack Developer",
    description: "Personal portfolio showcasing modern web development projects and skills in HTML5, Tailwind CSS, and JavaScript.",
    openGraph: {
      type: "website",
      locale: "en_US",
      url: "https://syaamil.dev",
      siteName: "Muhammad Syaamil Muzhaffar Portfolio",
      images: [
        {
          url: "https://syaamil.dev/assets/images/og-image.jpg",
          width: 1200,
          height: 630,
          alt: "Muhammad Syaamil Muzhaffar - Full Stack Developer"
        }
      ]
    },
    twitter: {
      handle: "@syaamil_dev",
      site: "@syaamil_dev",
      cardType: "summary_large_image"
    },
    additionalMetaTags: [
      {
        name: "viewport",
        content: "width=device-width, initial-scale=1.0"
      },
      {
        name: "theme-color",
        content: "#3b82f6"
      },
      {
        name: "msapplication-TileColor",
        content: "#3b82f6"
      },
      {
        name: "robots",
        content: "index, follow"
      },
      {
        name: "googlebot",
        content: "index, follow"
      }
    ]
  },

  // Social Media Links
  social: {
    github: "https://github.com/syaamil",
    instagram: "https://instagram.com/syaamil.dev",
    medium: "https://medium.com/@syaamil",
    twitter: "https://x.com/syaamil_dev",
    credly: "https://credly.com/users/syaamil-muzhaffar",
    spotify: "https://open.spotify.com/user/syaamil",
    linkedin: "https://linkedin.com/in/syaamil",
    email: "mailto:<EMAIL>",
    whatsapp: "https://wa.me/62xxxxxxxxxx"
  },

  // Navigation Configuration
  navigation: {
    main: [
      { name: "Home", href: "#home", icon: "home" },
      { name: "About", href: "#about", icon: "user" },
      { name: "Skills", href: "#skills", icon: "code" },
      { name: "Projects", href: "#projects", icon: "briefcase" },
      { name: "Contact", href: "#contact", icon: "mail" }
    ],
    footer: [
      { name: "Privacy Policy", href: "/privacy" },
      { name: "Terms of Service", href: "/terms" },
      { name: "Sitemap", href: "/sitemap.xml" }
    ]
  },

  // Performance Configuration
  performance: {
    // Image optimization
    images: {
      quality: 85,
      formats: ["webp", "jpg", "png"],
      sizes: [320, 640, 768, 1024, 1280, 1920],
      lazy: true
    },
    
    // Caching
    cache: {
      static: "1y",
      dynamic: "1h",
      api: "5m"
    },
    
    // Compression
    compression: {
      gzip: true,
      brotli: true
    }
  },

  // Security Configuration
  security: {
    // Content Security Policy
    csp: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"]
    },
    
    // Security headers
    headers: {
      "X-Frame-Options": "DENY",
      "X-Content-Type-Options": "nosniff",
      "X-XSS-Protection": "1; mode=block",
      "Referrer-Policy": "strict-origin-when-cross-origin",
      "Permissions-Policy": "camera=(), microphone=(), geolocation=()"
    },
    
    // Protection features
    protection: {
      disableRightClick: true,
      disableInspect: true,
      disableCopyPaste: true,
      disableKeyboardShortcuts: true,
      obfuscateContent: true
    }
  },

  // Analytics Configuration
  analytics: {
    // Google Analytics
    googleAnalytics: {
      id: "GA_MEASUREMENT_ID",
      enabled: false
    },
    
    // Custom analytics
    custom: {
      enabled: true,
      trackPageViews: true,
      trackEvents: true,
      trackPerformance: true
    }
  },

  // Development Configuration
  development: {
    // Hot reload
    hotReload: true,
    
    // Source maps
    sourceMaps: true,
    
    // Debug mode
    debug: false,
    
    // Mock data
    useMockData: false
  },

  // Build Configuration
  build: {
    // Output directory
    outDir: "dist",
    
    // Asset directory
    assetsDir: "assets",
    
    // Minification
    minify: true,
    
    // Tree shaking
    treeShaking: true,
    
    // Code splitting
    codeSplitting: false,
    
    // Bundle analysis
    analyze: false
  },

  // Deployment Configuration
  deployment: {
    // Target platform
    platform: "github-pages",
    
    // Base path
    basePath: "/",
    
    // Asset prefix
    assetPrefix: "",
    
    // Trailing slash
    trailingSlash: false
  },

  // Feature Flags
  features: {
    // Dark mode
    darkMode: false,
    
    // Internationalization
    i18n: false,
    
    // Progressive Web App
    pwa: false,
    
    // Service Worker
    serviceWorker: false,
    
    // Offline support
    offline: false,
    
    // Push notifications
    notifications: false
  },

  // Contact Form Configuration
  contact: {
    // Form provider
    provider: "netlify", // or "formspree", "emailjs", etc.
    
    // Form settings
    settings: {
      honeypot: true,
      recaptcha: false,
      validation: true,
      autoReply: true
    },
    
    // Email settings
    email: {
      to: "<EMAIL>",
      subject: "New Contact Form Submission",
      template: "default"
    }
  }
};
