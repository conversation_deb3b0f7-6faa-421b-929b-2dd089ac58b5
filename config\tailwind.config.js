/**
 * Tailwind CSS Configuration
 * Author: <PERSON>
 * Version: 4.1.11
 * Description: Custom Tailwind CSS configuration for Personal Portfolio
 */

export default {
  // Content paths for Tailwind to scan
  content: [
    "./index.html",
    "./src/**/*.{html,js,css}",
    "./src/components/**/*.html",
    "./dist/**/*.html",
  ],

  // Custom theme configuration
  theme: {
    extend: {
      // Custom colors
      colors: {
        primary: {
          50: "#eff6ff",
          100: "#dbeafe",
          200: "#bfdbfe",
          300: "#93c5fd",
          400: "#60a5fa",
          500: "#3b82f6",
          600: "#2563eb",
          700: "#1d4ed8",
          800: "#1e40af",
          900: "#1e3a8a",
        },
        secondary: {
          50: "#f8fafc",
          100: "#f1f5f9",
          200: "#e2e8f0",
          300: "#cbd5e1",
          400: "#94a3b8",
          500: "#64748b",
          600: "#475569",
          700: "#334155",
          800: "#1e293b",
          900: "#0f172a",
        },
      },

      // Custom fonts
      fontFamily: {
        sans: ["Inter", "ui-sans-serif", "system-ui", "sans-serif"],
        mono: ["JetBrains Mono", "ui-monospace", "monospace"],
      },

      // Custom spacing
      spacing: {
        18: "4.5rem",
        88: "22rem",
        128: "32rem",
      },

      // Custom animations
      animation: {
        "fade-in": "fadeIn 0.6s ease-in-out",
        "slide-up": "slideUp 0.8s ease-out",
        "slide-down": "slideDown 0.8s ease-out",
        "scale-in": "scaleIn 0.5s ease-out",
        "bounce-slow": "bounce 2s infinite",
      },

      // Custom keyframes
      keyframes: {
        fadeIn: {
          "0%": { opacity: "0" },
          "100%": { opacity: "1" },
        },
        slideUp: {
          "0%": { opacity: "0", transform: "translateY(30px)" },
          "100%": { opacity: "1", transform: "translateY(0)" },
        },
        slideDown: {
          "0%": { opacity: "0", transform: "translateY(-30px)" },
          "100%": { opacity: "1", transform: "translateY(0)" },
        },
        scaleIn: {
          "0%": { opacity: "0", transform: "scale(0.9)" },
          "100%": { opacity: "1", transform: "scale(1)" },
        },
      },

      // Custom shadows
      boxShadow: {
        custom: "0 10px 25px -5px rgba(59, 130, 246, 0.1)",
        "custom-lg": "0 20px 40px -10px rgba(59, 130, 246, 0.2)",
        "inner-custom": "inset 0 2px 4px 0 rgba(59, 130, 246, 0.1)",
      },

      // Custom gradients
      backgroundImage: {
        "gradient-primary": "linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)",
        "gradient-secondary":
          "linear-gradient(135deg, #64748b 0%, #334155 100%)",
        "gradient-radial":
          "radial-gradient(ellipse at center, var(--tw-gradient-stops))",
      },

      // Custom backdrop blur
      backdropBlur: {
        xs: "2px",
      },

      // Custom aspect ratios
      aspectRatio: {
        golden: "1.618",
        "4/3": "4 / 3",
        "3/2": "3 / 2",
      },
    },
  },

  // Plugins
  plugins: [
    // Add any additional plugins here
  ],

  // Dark mode configuration
  darkMode: "class",

  // Important prefix (if needed)
  // important: true,

  // Prefix for all classes (if needed)
  // prefix: 'tw-',

  // Separator for responsive prefixes
  separator: ":",

  // Core plugins to disable (if needed)
  corePlugins: {
    // preflight: false,
  },
};
