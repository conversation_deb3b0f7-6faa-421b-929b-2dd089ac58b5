# Setup Guide - Personal Portfolio

This guide will help you set up the Personal Portfolio project for development and customization.

## 📋 Prerequisites

Before you begin, ensure you have the following installed on your system:

### Required Software
- **Node.js** (v16.0.0 or higher)
  - Download from [nodejs.org](https://nodejs.org/)
  - Verify installation: `node --version`
- **npm** (comes with Node.js) or **yarn**
  - Verify npm: `npm --version`
  - Or install yarn: `npm install -g yarn`
- **Git** (for version control)
  - Download from [git-scm.com](https://git-scm.com/)
  - Verify installation: `git --version`

### Recommended Tools
- **VS Code** with extensions:
  - Tailwind CSS IntelliSense
  - Prettier - Code formatter
  - ES6+ snippets
  - Live Server
- **Modern web browser** (Chrome, Firefox, Safari, Edge)

## 🚀 Installation Steps

### 1. Clone the Repository

```bash
# Clone the repository
git clone https://github.com/syaamil/personal-portfolio.git

# Navigate to project directory
cd personal-portfolio

# Verify project structure
ls -la
```

### 2. Install Dependencies

```bash
# Install all dependencies
npm install

# Or using yarn
yarn install
```

**Dependencies installed:**
- `tailwindcss@^4.1.11` - CSS framework
- `@tailwindcss/cli@^4.1.11` - Tailwind CLI
- `prettier@^3.6.2` - Code formatter
- `prettier-plugin-tailwindcss@^0.6.14` - Tailwind formatting

### 3. Initial Setup

```bash
# Create necessary directories (if not exists)
mkdir -p dist/css dist/js dist/assets

# Build CSS for the first time
npm run build:css

# Or using npx directly
npx tailwindcss -i ./src/css/input.css -o ./dist/css/output.css --config ./config/tailwind.config.js
```

### 4. Start Development Server

```bash
# Start development server
npm run dev

# Or using Node.js directly
node tools/dev.js
```

The development server will:
- Start on `http://localhost:3000`
- Watch for file changes
- Auto-reload the browser
- Rebuild CSS automatically

## 🔧 Configuration

### Environment Setup

Create a `.env` file in the root directory (optional):

```env
# Development settings
NODE_ENV=development
PORT=3000
HOST=localhost

# Site settings
SITE_URL=http://localhost:3000
SITE_NAME="Muhammad Syaamil Muzhaffar Portfolio"

# Analytics (optional)
GA_TRACKING_ID=your-ga-id

# Contact form (optional)
CONTACT_FORM_ENDPOINT=your-form-endpoint
```

### VS Code Settings

Create `.vscode/settings.json` for optimal development experience:

```json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll": true
  },
  "tailwindCSS.includeLanguages": {
    "html": "html",
    "javascript": "javascript"
  },
  "tailwindCSS.experimental.classRegex": [
    ["class:\\s*?[\"'`]([^\"'`]*).*?[\"'`]", "[\"'`]([^\"'`]*)[\"'`]"]
  ]
}
```

### Git Configuration

```bash
# Set up Git (if not already configured)
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"

# Create .gitignore (already included)
# Add any additional files you want to ignore
```

## 📝 Customization Guide

### 1. Personal Information

Edit `src/data/profile.json`:

```json
{
  "personal": {
    "name": "Your Full Name",
    "title": "Your Professional Title",
    "subtitle": "Your tagline or brief description",
    "description": "Detailed description about yourself",
    "location": "Your Location",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "website": "https://yourwebsite.com",
    "profileImage": "assets/images/profile/your-photo.jpg"
  }
}
```

### 2. Skills & Technologies

Update `src/data/skills.json`:

```json
{
  "technical": [
    {
      "category": "Your Skill Category",
      "skills": [
        {
          "name": "Technology Name",
          "level": 85,
          "icon": "assets/icons/tech/technology.svg",
          "description": "Brief description of your experience"
        }
      ]
    }
  ]
}
```

### 3. Projects Portfolio

Modify `src/data/projects.json`:

```json
{
  "featured": [
    {
      "id": "unique-project-id",
      "title": "Project Title",
      "description": "Detailed project description",
      "image": "assets/images/projects/project-screenshot.jpg",
      "technologies": ["Tech1", "Tech2", "Tech3"],
      "features": ["Feature 1", "Feature 2"],
      "liveUrl": "https://project-demo.com",
      "githubUrl": "https://github.com/username/project",
      "status": "Completed",
      "category": "Web Development",
      "year": "2024"
    }
  ]
}
```

### 4. Social Media Links

Update `src/data/social.json`:

```json
{
  "platforms": [
    {
      "name": "GitHub",
      "username": "yourusername",
      "url": "https://github.com/yourusername",
      "icon": "assets/icons/social/github.svg",
      "description": "View my code repositories",
      "primary": true
    }
  ]
}
```

### 5. Styling Customization

Edit `config/tailwind.config.js`:

```javascript
export default {
  theme: {
    extend: {
      colors: {
        primary: {
          500: '#your-brand-color',
          600: '#darker-shade',
        },
        secondary: {
          500: '#your-secondary-color',
        }
      },
      fontFamily: {
        'sans': ['Your-Font', 'fallback-font'],
      }
    }
  }
}
```

## 🖼️ Asset Management

### Adding Images

1. **Profile Photos**: Place in `assets/images/profile/`
2. **Project Screenshots**: Place in `assets/images/projects/`
3. **Certificates**: Place in `assets/images/certificates/`

### Adding Icons

1. **Social Media Icons**: Place in `assets/icons/social/`
2. **Technology Icons**: Place in `assets/icons/tech/`
3. **UI Icons**: Place in `assets/icons/ui/`

**Icon Requirements:**
- Format: SVG (preferred) or PNG
- Size: 24x24px minimum
- Optimized for web

### Adding Documents

Place resume, CV, and other documents in `assets/files/`

## 🧪 Testing & Development

### Development Commands

```bash
# Start development server
npm run dev

# Build CSS only
npm run build:css

# Watch CSS changes
npm run watch:css

# Format code
npm run format

# Lint code (if configured)
npm run lint
```

### Testing Checklist

- [ ] All pages load correctly
- [ ] Navigation works smoothly
- [ ] Images display properly
- [ ] Forms function correctly
- [ ] Responsive design works on all devices
- [ ] Performance is optimized
- [ ] SEO meta tags are correct

### Browser Testing

Test your portfolio on:
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)
- [ ] Mobile browsers (iOS Safari, Chrome Mobile)

## 🚀 Production Build

### Building for Production

```bash
# Create production build
npm run build

# Optimize assets
npm run optimize

# Preview production build
npm run preview
```

### Pre-deployment Checklist

- [ ] All personal information is updated
- [ ] All links work correctly
- [ ] Images are optimized
- [ ] Meta tags are configured
- [ ] Analytics tracking is set up (if desired)
- [ ] Contact form is configured
- [ ] Performance is optimized

## 🔧 Troubleshooting

### Common Issues

**1. CSS not building:**
```bash
# Check Tailwind installation
npx tailwindcss --help

# Rebuild CSS manually
npx tailwindcss -i ./src/css/input.css -o ./dist/css/output.css --config ./config/tailwind.config.js
```

**2. Development server not starting:**
```bash
# Check if port is in use
lsof -i :3000

# Use different port
PORT=3001 npm run dev
```

**3. Images not loading:**
- Check file paths in JSON data files
- Ensure images exist in correct directories
- Verify file extensions match

**4. JavaScript errors:**
- Check browser console for errors
- Ensure all JSON files have valid syntax
- Verify file paths are correct

### Getting Help

If you encounter issues:

1. Check the [README.md](../README.md) for additional information
2. Review the browser console for error messages
3. Ensure all dependencies are installed correctly
4. Verify file paths and naming conventions

## 📚 Additional Resources

- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [MDN Web Docs](https://developer.mozilla.org/)
- [Web.dev Performance Guide](https://web.dev/performance/)
- [Accessibility Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)

---

🎉 **You're all set!** Start customizing your portfolio and make it uniquely yours.
