<!doctype html>
<html lang="en" class="scroll-smooth">
  <head>
    <!-- Meta Tags -->
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta
      name="description"
      content="Personal portfolio of <PERSON>, a passionate Full Stack Developer specializing in modern web technologies including HTML5, Tailwind CSS, and Vanilla JavaScript."
    />
    <meta
      name="keywords"
      content="<PERSON>, Full Stack Developer, Web Developer, Frontend Developer, HTML5, CSS3, Tailwind CSS, JavaScript, Vanilla JavaScript, Node.js, Portfolio, Indonesia Developer"
    />
    <meta name="author" content="<PERSON>" />
    <meta name="robots" content="index, follow" />
    <meta name="theme-color" content="#3b82f6" />

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<PERSON> - Full Stack Developer" />
    <meta
      property="og:description"
      content="Personal portfolio showcasing modern web development projects and skills in HTML5, Tailwind CSS, and JavaScript."
    />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://syaamil.dev" />
    <meta property="og:image" content="https://syaamil.dev/assets/images/og-image.jpg" />
    <meta property="og:site_name" content="Muhammad Syaamil Muzhaffar Portfolio" />

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Muhammad Syaamil Muzhaffar - Full Stack Developer" />
    <meta
      name="twitter:description"
      content="Personal portfolio showcasing modern web development projects and skills"
    />
    <meta name="twitter:image" content="https://syaamil.dev/assets/images/og-image.jpg" />
    <meta name="twitter:creator" content="@syaamil_dev" />

    <!-- Canonical URL -->
    <link rel="canonical" href="https://syaamil.dev" />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/icons/apple-touch-icon.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/assets/icons/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/assets/icons/favicon-16x16.png" />

    <!-- Preconnect for Performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

    <!-- Stylesheets -->
    <link href="dist/css/output.css" rel="stylesheet" />

    <!-- Title -->
    <title>Muhammad Syaamil Muzhaffar - Full Stack Developer | Portfolio</title>

    <!-- Structured Data -->
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "Person",
        "name": "Muhammad Syaamil Muzhaffar",
        "jobTitle": "Full Stack Developer",
        "description": "Passionate Full Stack Developer specializing in modern web technologies",
        "url": "https://syaamil.dev",
        "image": "https://syaamil.dev/assets/images/profile/muhammadsyaamilmuzhaffar.png",
        "sameAs": [
          "https://github.com/syaamil",
          "https://instagram.com/syaamil.dev",
          "https://medium.com/@syaamil",
          "https://x.com/syaamil_dev",
          "https://credly.com/users/syaamil-muzhaffar"
        ],
        "address": {
          "@type": "PostalAddress",
          "addressLocality": "Jakarta",
          "addressCountry": "Indonesia"
        },
        "email": "<EMAIL>",
        "knowsAbout": [
          "HTML5",
          "CSS3",
          "Tailwind CSS",
          "JavaScript",
          "Node.js",
          "Web Development",
          "Frontend Development",
          "Full Stack Development"
        ]
      }
    </script>
  </head>

  <body class="no-select no-context-menu bg-white font-sans text-gray-900 antialiased">
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-overlay">
      <div class="text-center">
        <div class="loading-spinner mx-auto mb-4"></div>
        <p class="font-medium text-gray-600">Loading Portfolio...</p>
        <p class="mt-2 text-sm text-gray-500">Muhammad Syaamil Muzhaffar</p>
      </div>
    </div>

    <!-- Main Content -->
    <main id="main-content" class="opacity-0 transition-opacity duration-500">
      <!-- Hero Section -->
      <section
        id="home"
        class="flex-center relative min-h-screen overflow-hidden bg-gradient-to-br from-blue-50 via-white to-purple-50"
      >
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-5">
          <div
            class="absolute inset-0"
            style="
              background-image: radial-gradient(circle at 1px 1px, rgba(59, 130, 246, 0.3) 1px, transparent 0);
              background-size: 20px 20px;
            "
          ></div>
        </div>

        <div class="container-custom relative z-10 text-center">
          <div class="mx-auto max-w-4xl">
            <!-- Profile Image -->
            <div class="fade-in mb-8">
              <img
                src="assets/images/profile/muhammadsyaamilmuzhaffar.png"
                alt="Muhammad Syaamil Muzhaffar - Full Stack Developer"
                class="shadow-custom mx-auto mb-6 h-32 w-32 rounded-full border-4 border-white object-cover md:h-40 md:w-40"
              />
            </div>

            <!-- Main Heading -->
            <h1 class="hero-title fade-in mb-6">Muhammad Syaamil Muzhaffar</h1>

            <!-- Subtitle -->
            <p class="hero-subtitle fade-in mb-8">Full Stack Developer & Digital Innovation Enthusiast</p>

            <!-- Description -->
            <p class="fade-in mx-auto mb-12 max-w-3xl text-lg leading-relaxed text-gray-600 md:text-xl">
              Passionate about creating innovative digital solutions with modern web technologies. Specializing in
              responsive design, performance optimization, and exceptional user experiences.
            </p>

            <!-- Call to Action Buttons -->
            <div class="fade-in mb-16 flex flex-col justify-center gap-4 sm:flex-row">
              <a href="#projects" class="btn-primary">
                <svg class="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                  />
                </svg>
                View My Work
              </a>
              <a href="#contact" class="btn-secondary">
                <svg class="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                  />
                </svg>
                Get In Touch
              </a>
            </div>

            <!-- Quick Stats -->
            <div class="fade-in mx-auto grid max-w-2xl grid-cols-2 gap-6 md:grid-cols-4">
              <div class="text-center">
                <div class="mb-1 text-2xl font-bold text-blue-600 md:text-3xl">3+</div>
                <div class="text-sm text-gray-600">Years Experience</div>
              </div>
              <div class="text-center">
                <div class="mb-1 text-2xl font-bold text-blue-600 md:text-3xl">50+</div>
                <div class="text-sm text-gray-600">Projects Completed</div>
              </div>
              <div class="text-center">
                <div class="mb-1 text-2xl font-bold text-blue-600 md:text-3xl">30+</div>
                <div class="text-sm text-gray-600">Happy Clients</div>
              </div>
              <div class="text-center">
                <div class="mb-1 text-2xl font-bold text-blue-600 md:text-3xl">15+</div>
                <div class="text-sm text-gray-600">Technologies</div>
              </div>
            </div>
          </div>

          <!-- Scroll Indicator -->
          <div class="absolute bottom-8 left-1/2 -translate-x-1/2 transform animate-bounce">
            <a href="#about" class="text-gray-400 transition-colors hover:text-blue-600">
              <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
              </svg>
            </a>
          </div>
        </div>
      </section>

      <!-- About Section Placeholder -->
      <section id="about" class="section-container bg-gray-50">
        <div class="text-center">
          <h2 class="section-title">About Me</h2>
          <p class="section-subtitle">Learn more about my journey, passion, and expertise in web development</p>
          <div class="mx-auto max-w-4xl rounded-xl bg-white p-8 shadow-lg">
            <p class="text-lg leading-relaxed text-gray-600">
              This section will be dynamically populated with content from the data files. The component system will
              render the complete about section with personal information, education, certifications, and more.
            </p>
          </div>
        </div>
      </section>

      <!-- Skills Section Placeholder -->
      <section id="skills" class="section-container bg-white">
        <div class="text-center">
          <h2 class="section-title">Skills & Expertise</h2>
          <p class="section-subtitle">Technologies and tools I work with to bring ideas to life</p>
          <div class="mx-auto max-w-4xl rounded-xl bg-gray-50 p-8">
            <p class="text-lg leading-relaxed text-gray-600">
              This section will be dynamically populated with skills data. The component system will render interactive
              skill bars, technology icons, and expertise areas from the skills.json file.
            </p>
          </div>
        </div>
      </section>

      <!-- Projects Section Placeholder -->
      <section id="projects" class="section-container bg-gray-50">
        <div class="text-center">
          <h2 class="section-title">Featured Projects</h2>
          <p class="section-subtitle">A showcase of my recent work and creative solutions</p>
          <div class="mx-auto max-w-4xl rounded-xl bg-white p-8 shadow-lg">
            <p class="text-lg leading-relaxed text-gray-600">
              This section will be dynamically populated with project data. The component system will render project
              cards, galleries, and detailed information from the projects.json file.
            </p>
          </div>
        </div>
      </section>

      <!-- Contact Section Placeholder -->
      <section id="contact" class="section-container bg-white">
        <div class="text-center">
          <h2 class="section-title">Get In Touch</h2>
          <p class="section-subtitle">Let's discuss your next project or collaboration opportunity</p>
          <div class="mx-auto max-w-4xl rounded-xl bg-gray-50 p-8">
            <p class="text-lg leading-relaxed text-gray-600">
              This section will be dynamically populated with contact information and form. The component system will
              render social links, contact details, and an interactive contact form from the social.json file.
            </p>
          </div>
        </div>
      </section>
    </main>

    <!-- JavaScript Files -->
    <script src="dist/js/utils.js"></script>
    <script src="dist/js/components.js"></script>
    <script src="dist/js/main.js"></script>

    <!-- Initialize Application -->
    <script>
      // Application initialization will be handled by main.js
      // This ensures all components are loaded and security measures are in place

      // Hide loading screen once everything is ready
      document.addEventListener('DOMContentLoaded', function () {
        const loadingScreen = document.getElementById('loading-screen');
        const mainContent = document.getElementById('main-content');

        // Minimum loading time for better UX
        setTimeout(() => {
          if (loadingScreen) {
            loadingScreen.style.opacity = '0';
            setTimeout(() => {
              loadingScreen.style.display = 'none';
              if (mainContent) {
                mainContent.classList.remove('opacity-0');
                mainContent.classList.add('opacity-100');
              }
            }, 300);
          }
        }, 1000);
      });
    </script>
  </body>
</html>
