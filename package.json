{"name": "personal-portfolio", "version": "1.0.0", "description": "Personal portfolio website for <PERSON> - Full Stack Developer", "main": "index.html", "type": "module", "scripts": {"dev": "node tools/dev.js", "build": "node tools/build.js", "build:css": "tailwindcss -i ./src/css/input.css -o ./dist/css/output.css --config ./config/tailwind.config.js", "build:css:watch": "tailwindcss -i ./src/css/input.css -o ./dist/css/output.css --config ./config/tailwind.config.js --watch", "build:css:min": "tailwindcss -i ./src/css/input.css -o ./dist/css/output.css --config ./config/tailwind.config.js --minify", "optimize": "node tools/optimize.js", "preview": "node tools/dev.js --production", "clean": "<PERSON><PERSON><PERSON> dist", "format": "prettier --write .", "format:check": "prettier --check .", "lint": "echo '<PERSON><PERSON> not configured yet'", "test": "echo 'Tests not configured yet'", "start": "npm run dev", "deploy": "npm run build && npm run optimize"}, "keywords": ["portfolio", "personal-website", "muh<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON>", "full-stack-developer", "web-developer", "html5", "tailwindcss", "javascript", "responsive-design", "modern-web"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://syaamil.dev"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/syaamil/personal-portfolio.git"}, "bugs": {"url": "https://github.com/syaamil/personal-portfolio/issues"}, "homepage": "https://syaamil.dev", "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "dependencies": {"@tailwindcss/cli": "^4.1.11", "tailwindcss": "^4.1.11"}, "devDependencies": {"prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}