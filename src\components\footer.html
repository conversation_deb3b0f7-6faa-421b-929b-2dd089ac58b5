<!-- Footer Component for Personal Portfolio -->
<!-- Author: <PERSON> -->

<footer class="bg-gray-900 text-white no-select no-context-menu" role="contentinfo">
  <div class="container-custom py-12">
    <div class="grid md:grid-cols-4 gap-8">
      
      <!-- Brand & Description -->
      <div class="md:col-span-2">
        <div class="flex items-center space-x-3 mb-4">
          <div class="w-10 h-10 bg-gradient-primary rounded-lg flex-center">
            <span class="text-white font-bold text-lg">SM</span>
          </div>
          <div>
            <h3 class="font-semibold text-xl"><PERSON></h3>
            <p class="text-gray-400 text-sm">Full Stack Developer</p>
          </div>
        </div>
        <p class="text-gray-300 mb-6 max-w-md leading-relaxed">
          Passionate about creating innovative digital solutions with modern web technologies. 
          Specializing in responsive design, performance optimization, and user experience.
        </p>
        
        <!-- Social Media Links -->
        <div class="flex items-center space-x-4">
          <a href="https://github.com/syaamil" 
             target="_blank" 
             rel="noopener noreferrer"
             class="text-gray-400 hover:text-white transition-colors p-2 rounded-lg hover:bg-gray-800"
             aria-label="Visit GitHub profile">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path d="M12 0C5.37 0 0 5.37 0 12c0 5.31 3.435 9.795 8.205 11.385.6.105.825-.255.825-.57 0-.285-.015-1.23-.015-2.235-3.015.555-3.795-.735-4.035-1.41-.135-.345-.72-1.41-1.23-1.695-.42-.225-1.02-.78-.015-.795.945-.015 1.62.87 1.845 1.23 1.08 1.815 2.805 1.305 3.495.99.105-.78.42-1.305.765-1.605-2.67-.3-5.46-1.335-5.46-5.925 0-1.305.465-2.385 1.23-3.225-.12-.3-.54-1.53.12-3.18 0 0 1.005-.315 3.3 1.23.96-.27 1.98-.405 3-.405s2.04.135 3 .405c2.295-1.56 3.3-1.23 3.3-1.23.66 1.65.24 2.88.12 3.18.765.84 1.23 1.905 1.23 3.225 0 4.605-2.805 5.625-5.475 5.925.435.375.81 1.095.81 2.22 0 1.605-.015 2.895-.015 3.3 0 .315.225.69.825.57A12.02 12.02 0 0024 12c0-6.63-5.37-12-12-12z"/>
            </svg>
          </a>
          
          <a href="https://instagram.com/syaamil.dev" 
             target="_blank" 
             rel="noopener noreferrer"
             class="text-gray-400 hover:text-white transition-colors p-2 rounded-lg hover:bg-gray-800"
             aria-label="Visit Instagram profile">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.004 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.807.875 1.297 2.026 1.297 3.323s-.49 2.448-1.297 3.323c-.875.807-2.026 1.297-3.323 1.297zm7.718-1.297c-.875.807-2.026 1.297-3.323 1.297s-2.448-.49-3.323-1.297c-.807-.875-1.297-2.026-1.297-3.323s.49-2.448 1.297-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.807.875 1.297 2.026 1.297 3.323s-.49 2.448-1.297 3.323z"/>
            </svg>
          </a>
          
          <a href="https://medium.com/@syaamil" 
             target="_blank" 
             rel="noopener noreferrer"
             class="text-gray-400 hover:text-white transition-colors p-2 rounded-lg hover:bg-gray-800"
             aria-label="Visit Medium profile">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path d="M13.54 12a6.8 6.8 0 01-6.77 6.82A6.8 6.8 0 010 12a6.8 6.8 0 016.77-6.82A6.8 6.8 0 0113.54 12zM20.96 12c0 3.54-1.51 6.42-3.38 6.42-1.87 0-3.39-2.88-3.39-6.42s1.52-6.42 3.39-6.42 3.38 2.88 3.38 6.42M24 12c0 3.17-.53 5.75-1.19 5.75-.66 0-1.19-2.58-1.19-5.75s.53-5.75 1.19-5.75C23.47 6.25 24 8.83 24 12z"/>
            </svg>
          </a>
          
          <a href="https://x.com/syaamil_dev" 
             target="_blank" 
             rel="noopener noreferrer"
             class="text-gray-400 hover:text-white transition-colors p-2 rounded-lg hover:bg-gray-800"
             aria-label="Visit X (Twitter) profile">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
            </svg>
          </a>
          
          <a href="https://credly.com/users/syaamil-muzhaffar" 
             target="_blank" 
             rel="noopener noreferrer"
             class="text-gray-400 hover:text-white transition-colors p-2 rounded-lg hover:bg-gray-800"
             aria-label="Visit Credly profile">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm5.568 8.16l-6.222 6.222-3.346-3.346 1.414-1.414 1.932 1.932 4.808-4.808 1.414 1.414z"/>
            </svg>
          </a>
        </div>
      </div>
      
      <!-- Quick Links -->
      <div>
        <h4 class="font-semibold text-lg mb-4">Quick Links</h4>
        <nav class="space-y-3" role="navigation" aria-label="Footer navigation">
          <a href="#home" class="block text-gray-400 hover:text-white transition-colors">Home</a>
          <a href="#about" class="block text-gray-400 hover:text-white transition-colors">About</a>
          <a href="#skills" class="block text-gray-400 hover:text-white transition-colors">Skills</a>
          <a href="#projects" class="block text-gray-400 hover:text-white transition-colors">Projects</a>
          <a href="#contact" class="block text-gray-400 hover:text-white transition-colors">Contact</a>
        </nav>
      </div>
      
      <!-- Contact Info -->
      <div>
        <h4 class="font-semibold text-lg mb-4">Get In Touch</h4>
        <div class="space-y-3">
          <div class="flex items-center space-x-3">
            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
            </svg>
            <a href="mailto:<EMAIL>" class="text-gray-400 hover:text-white transition-colors">
              <EMAIL>
            </a>
          </div>
          
          <div class="flex items-center space-x-3">
            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
            </svg>
            <span class="text-gray-400">Jakarta, Indonesia</span>
          </div>
          
          <div class="flex items-center space-x-3">
            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"/>
            </svg>
            <a href="https://syaamil.dev" class="text-gray-400 hover:text-white transition-colors">
              syaamil.dev
            </a>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Bottom Section -->
    <div class="border-t border-gray-800 mt-12 pt-8">
      <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
        
        <!-- Copyright -->
        <div class="text-gray-400 text-sm">
          <p>&copy; 2024 Muhammad Syaamil Muzhaffar. All rights reserved.</p>
        </div>
        
        <!-- Tech Stack -->
        <div class="flex items-center space-x-4 text-gray-400 text-sm">
          <span>Built with</span>
          <div class="flex items-center space-x-2">
            <img src="assets/icons/tech/html5.svg" alt="HTML5" class="w-4 h-4" title="HTML5">
            <img src="assets/icons/tech/tailwindcss.svg" alt="Tailwind CSS" class="w-4 h-4" title="Tailwind CSS">
            <img src="assets/icons/tech/javascript.svg" alt="JavaScript" class="w-4 h-4" title="JavaScript">
          </div>
        </div>
        
        <!-- Legal Links -->
        <div class="flex items-center space-x-6 text-gray-400 text-sm">
          <a href="/privacy" class="hover:text-white transition-colors">Privacy Policy</a>
          <a href="/terms" class="hover:text-white transition-colors">Terms of Service</a>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Back to Top Button -->
  <button id="back-to-top" 
          class="fixed bottom-8 right-8 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-all duration-300 opacity-0 invisible"
          aria-label="Back to top">
    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"/>
    </svg>
  </button>
</footer>

<script>
// Back to top button functionality
(function() {
  const backToTopBtn = document.getElementById('back-to-top');
  
  if (backToTopBtn) {
    // Show/hide button based on scroll position
    window.addEventListener('scroll', function() {
      if (window.pageYOffset > 300) {
        backToTopBtn.classList.remove('opacity-0', 'invisible');
        backToTopBtn.classList.add('opacity-100', 'visible');
      } else {
        backToTopBtn.classList.add('opacity-0', 'invisible');
        backToTopBtn.classList.remove('opacity-100', 'visible');
      }
    });
    
    // Smooth scroll to top
    backToTopBtn.addEventListener('click', function() {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    });
  }
})();
</script>
