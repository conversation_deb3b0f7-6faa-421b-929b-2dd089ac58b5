<!-- Header Component for Personal Portfolio -->
<!-- Author: <PERSON> -->

<header class="fixed top-0 left-0 right-0 bg-white/90 backdrop-blur-md z-50 border-b border-gray-200 no-select no-context-menu">
  <div class="container-custom">
    <div class="flex-between py-4">
      <!-- Logo/Brand -->
      <div class="flex items-center space-x-3">
        <div class="w-10 h-10 bg-gradient-primary rounded-lg flex-center">
          <span class="text-white font-bold text-lg">SM</span>
        </div>
        <div class="hidden sm:block">
          <h1 class="font-semibold text-gray-900 text-lg">Syaamil</h1>
          <p class="text-xs text-gray-600">Full Stack Developer</p>
        </div>
      </div>
      
      <!-- Desktop Navigation -->
      <nav class="hidden md:flex items-center space-x-8" role="navigation" aria-label="Main navigation">
        <a href="#home" class="nav-link text-gray-700 hover:text-blue-600 transition-colors font-medium" aria-label="Go to Home section">
          Home
        </a>
        <a href="#about" class="nav-link text-gray-700 hover:text-blue-600 transition-colors font-medium" aria-label="Go to About section">
          About
        </a>
        <a href="#skills" class="nav-link text-gray-700 hover:text-blue-600 transition-colors font-medium" aria-label="Go to Skills section">
          Skills
        </a>
        <a href="#projects" class="nav-link text-gray-700 hover:text-blue-600 transition-colors font-medium" aria-label="Go to Projects section">
          Projects
        </a>
        <a href="#contact" class="nav-link text-gray-700 hover:text-blue-600 transition-colors font-medium" aria-label="Go to Contact section">
          Contact
        </a>
      </nav>
      
      <!-- Social Links & Mobile Menu -->
      <div class="flex items-center space-x-4">
        <!-- GitHub Link -->
        <a href="https://github.com/syaamil" 
           target="_blank" 
           rel="noopener noreferrer"
           class="text-gray-600 hover:text-blue-600 transition-colors p-2 rounded-lg hover:bg-gray-100"
           aria-label="Visit GitHub profile">
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path d="M12 0C5.37 0 0 5.37 0 12c0 5.31 3.435 9.795 8.205 11.385.6.105.825-.255.825-.57 0-.285-.015-1.23-.015-2.235-3.015.555-3.795-.735-4.035-1.41-.135-.345-.72-1.41-1.23-1.695-.42-.225-1.02-.78-.015-.795.945-.015 1.62.87 1.845 1.23 1.08 1.815 2.805 1.305 3.495.99.105-.78.42-1.305.765-1.605-2.67-.3-5.46-1.335-5.46-5.925 0-1.305.465-2.385 1.23-3.225-.12-.3-.54-1.53.12-3.18 0 0 1.005-.315 3.3 1.23.96-.27 1.98-.405 3-.405s2.04.135 3 .405c2.295-1.56 3.3-1.23 3.3-1.23.66 1.65.24 2.88.12 3.18.765.84 1.23 1.905 1.23 3.225 0 4.605-2.805 5.625-5.475 5.925.435.375.81 1.095.81 2.22 0 1.605-.015 2.895-.015 3.3 0 .315.225.69.825.57A12.02 12.02 0 0024 12c0-6.63-5.37-12-12-12z"/>
          </svg>
        </a>
        
        <!-- Theme Toggle (Future Enhancement) -->
        <button class="hidden text-gray-600 hover:text-blue-600 transition-colors p-2 rounded-lg hover:bg-gray-100" 
                aria-label="Toggle dark mode">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"/>
          </svg>
        </button>
        
        <!-- Mobile Menu Button -->
        <button class="md:hidden text-gray-600 hover:text-blue-600 transition-colors p-2 rounded-lg hover:bg-gray-100" 
                id="mobile-menu-btn"
                aria-label="Toggle mobile menu"
                aria-expanded="false"
                aria-controls="mobile-menu">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
          </svg>
        </button>
      </div>
    </div>
    
    <!-- Mobile Menu -->
    <div class="md:hidden hidden" id="mobile-menu" role="navigation" aria-label="Mobile navigation">
      <div class="py-4 space-y-4 border-t border-gray-200">
        <a href="#home" 
           class="block nav-link text-gray-700 hover:text-blue-600 transition-colors font-medium py-2"
           aria-label="Go to Home section">
          Home
        </a>
        <a href="#about" 
           class="block nav-link text-gray-700 hover:text-blue-600 transition-colors font-medium py-2"
           aria-label="Go to About section">
          About
        </a>
        <a href="#skills" 
           class="block nav-link text-gray-700 hover:text-blue-600 transition-colors font-medium py-2"
           aria-label="Go to Skills section">
          Skills
        </a>
        <a href="#projects" 
           class="block nav-link text-gray-700 hover:text-blue-600 transition-colors font-medium py-2"
           aria-label="Go to Projects section">
          Projects
        </a>
        <a href="#contact" 
           class="block nav-link text-gray-700 hover:text-blue-600 transition-colors font-medium py-2"
           aria-label="Go to Contact section">
          Contact
        </a>
        
        <!-- Mobile Social Links -->
        <div class="pt-4 border-t border-gray-200">
          <div class="flex items-center space-x-4">
            <a href="https://github.com/syaamil" 
               target="_blank" 
               rel="noopener noreferrer"
               class="text-gray-600 hover:text-blue-600 transition-colors"
               aria-label="Visit GitHub profile">
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path d="M12 0C5.37 0 0 5.37 0 12c0 5.31 3.435 9.795 8.205 11.385.6.105.825-.255.825-.57 0-.285-.015-1.23-.015-2.235-3.015.555-3.795-.735-4.035-1.41-.135-.345-.72-1.41-1.23-1.695-.42-.225-1.02-.78-.015-.795.945-.015 1.62.87 1.845 1.23 1.08 1.815 2.805 1.305 3.495.99.105-.78.42-1.305.765-1.605-2.67-.3-5.46-1.335-5.46-5.925 0-1.305.465-2.385 1.23-3.225-.12-.3-.54-1.53.12-3.18 0 0 1.005-.315 3.3 1.23.96-.27 1.98-.405 3-.405s2.04.135 3 .405c2.295-1.56 3.3-1.23 3.3-1.23.66 1.65.24 2.88.12 3.18.765.84 1.23 1.905 1.23 3.225 0 4.605-2.805 5.625-5.475 5.925.435.375.81 1.095.81 2.22 0 1.605-.015 2.895-.015 3.3 0 .315.225.69.825.57A12.02 12.02 0 0024 12c0-6.63-5.37-12-12-12z"/>
              </svg>
            </a>
            <a href="https://instagram.com/syaamil.dev" 
               target="_blank" 
               rel="noopener noreferrer"
               class="text-gray-600 hover:text-blue-600 transition-colors"
               aria-label="Visit Instagram profile">
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.004 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.807.875 1.297 2.026 1.297 3.323s-.49 2.448-1.297 3.323c-.875.807-2.026 1.297-3.323 1.297zm7.718-1.297c-.875.807-2.026 1.297-3.323 1.297s-2.448-.49-3.323-1.297c-.807-.875-1.297-2.026-1.297-3.323s.49-2.448 1.297-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.807.875 1.297 2.026 1.297 3.323s-.49 2.448-1.297 3.323z"/>
              </svg>
            </a>
            <a href="https://medium.com/@syaamil" 
               target="_blank" 
               rel="noopener noreferrer"
               class="text-gray-600 hover:text-blue-600 transition-colors"
               aria-label="Visit Medium profile">
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path d="M13.54 12a6.8 6.8 0 01-6.77 6.82A6.8 6.8 0 010 12a6.8 6.8 0 016.77-6.82A6.8 6.8 0 0113.54 12zM20.96 12c0 3.54-1.51 6.42-3.38 6.42-1.87 0-3.39-2.88-3.39-6.42s1.52-6.42 3.39-6.42 3.38 2.88 3.38 6.42M24 12c0 3.17-.53 5.75-1.19 5.75-.66 0-1.19-2.58-1.19-5.75s.53-5.75 1.19-5.75C23.47 6.25 24 8.83 24 12z"/>
              </svg>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</header>

<!-- Header Spacer to prevent content overlap -->
<div class="h-20" aria-hidden="true"></div>
