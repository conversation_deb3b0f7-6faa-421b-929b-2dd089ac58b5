/* Component-specific styles for Personal Portfolio */
/* Author: <PERSON> */
/* Framework: Tailwind CSS v4.1.11 */

/* Navigation Component */
.nav-link {
  transition: all 0.3s ease-in-out;
}

.nav-link:hover {
  color: #2563eb;
  transform: scale(1.05);
}

.nav-link.active {
  color: #2563eb;
  font-weight: 600;
}

/* Hero Section Component */
.hero-title {
  font-size: 2.25rem;
  font-weight: 700;
  background: linear-gradient(to right, #2563eb, #9333ea);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

@media (min-width: 768px) {
  .hero-title {
    font-size: 3.75rem;
  }
}

.hero-subtitle {
  font-size: 1.125rem;
  color: #4b5563;
  line-height: 1.625;
}

@media (min-width: 768px) {
  .hero-subtitle {
    font-size: 1.25rem;
  }
}

/* Button Components */
.btn-primary {
  padding: 0.75rem 1.5rem;
  background-color: #2563eb;
  color: white;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
}

.btn-primary:hover {
  background-color: #1d4ed8;
  transform: scale(1.05);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
  padding: 0.75rem 1.5rem;
  border: 2px solid #2563eb;
  color: #2563eb;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  background-color: transparent;
}

.btn-secondary:hover {
  background-color: #2563eb;
  color: white;
  transform: scale(1.05);
}

/* Section Components */
.section-container {
  max-width: 80rem;
  margin: 0 auto;
  padding: 4rem 1rem;
}

@media (min-width: 640px) {
  .section-container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .section-container {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

@media (min-width: 768px) {
  .section-container {
    padding-top: 6rem;
    padding-bottom: 6rem;
  }
}

.section-title {
  font-size: 1.875rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 3rem;
  color: #111827;
}

@media (min-width: 768px) {
  .section-title {
    font-size: 2.25rem;
  }
}

.section-subtitle {
  font-size: 1.125rem;
  color: #4b5563;
  text-align: center;
  margin-bottom: 4rem;
  max-width: 48rem;
  margin-left: auto;
  margin-right: auto;
}

/* Loading Component */
.loading-spinner {
  animation: spin 1s linear infinite;
  border-radius: 50%;
  height: 2rem;
  width: 2rem;
  border-bottom: 2px solid #2563eb;
  border-top: 2px solid transparent;
  border-left: 2px solid transparent;
  border-right: 2px solid transparent;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
}

/* Smooth Scroll Behavior */
html {
  scroll-behavior: smooth;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background-color: #f3f4f6;
}

::-webkit-scrollbar-thumb {
  border-radius: 9999px;
  background-color: #2563eb;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #1d4ed8;
}

/* Disable Text Selection for Security */
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Disable Right Click Context Menu */
.no-context-menu {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Responsive Design Utilities */
@media (max-width: 640px) {
  .section-container {
    padding-left: 1rem;
    padding-right: 1rem;
    padding-top: 3rem;
    padding-bottom: 3rem;
  }

  .hero-title {
    font-size: 1.875rem;
  }

  .section-title {
    font-size: 1.5rem;
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.6s ease-in-out;
}

.slide-up {
  animation: slideUp 0.8s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Performance Optimizations */
* {
  box-sizing: border-box;
}

img {
  max-width: 100%;
  height: auto;
  loading: lazy;
}

/* Focus States for Accessibility */
.focus-visible {
  outline: none;
  box-shadow:
    0 0 0 2px #3b82f6,
    0 0 0 4px rgba(59, 130, 246, 0.2);
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }
}
