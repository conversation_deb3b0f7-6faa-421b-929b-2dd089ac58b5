/* Custom Utility Classes for Personal Portfolio */
/* Author: <PERSON> */
/* Framework: Tailwind CSS v4.1.11 */

/* Custom Color Utilities */
.text-primary {
  @apply text-blue-600;
}

.text-secondary {
  @apply text-gray-600;
}

.bg-primary {
  @apply bg-blue-600;
}

.bg-secondary {
  @apply bg-gray-100;
}

/* Custom Gradient Utilities */
.gradient-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
}

.gradient-text {
  @apply bg-gradient-to-r from-blue-600 to-purple-600;
  @apply bg-clip-text text-transparent;
}

/* Custom Shadow Utilities */
.shadow-custom {
  box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.1);
}

.shadow-hover {
  box-shadow: 0 20px 40px -10px rgba(59, 130, 246, 0.2);
}

/* Custom Border Utilities */
.border-gradient {
  border: 2px solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(135deg, #3b82f6, #8b5cf6) border-box;
}

/* Custom Spacing Utilities */
.space-y-custom > * + * {
  margin-top: 1.5rem;
}

.space-x-custom > * + * {
  margin-left: 1.5rem;
}

/* Custom Typography Utilities */
.text-balance {
  text-wrap: balance;
}

.text-pretty {
  text-wrap: pretty;
}

/* Custom Layout Utilities */
.container-custom {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

.grid-auto-fit {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-auto-fill {
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
}

/* Custom Flexbox Utilities */
.flex-center {
  @apply flex items-center justify-center;
}

.flex-between {
  @apply flex items-center justify-between;
}

.flex-around {
  @apply flex items-center justify-around;
}

/* Custom Position Utilities */
.absolute-center {
  @apply absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2;
}

.fixed-center {
  @apply fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2;
}

/* Custom Transition Utilities */
.transition-custom {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-slow {
  transition: all 0.5s ease-in-out;
}

.transition-fast {
  transition: all 0.15s ease-in-out;
}

/* Custom Transform Utilities */
.scale-hover:hover {
  transform: scale(1.05);
}

.rotate-hover:hover {
  transform: rotate(5deg);
}

.translate-hover:hover {
  transform: translateY(-5px);
}

/* Custom Filter Utilities */
.blur-custom {
  filter: blur(8px);
}

.brightness-custom {
  filter: brightness(1.1);
}

.contrast-custom {
  filter: contrast(1.1);
}

/* Custom Backdrop Utilities */
.backdrop-custom {
  backdrop-filter: blur(10px) saturate(180%);
  background-color: rgba(255, 255, 255, 0.8);
}

/* Custom Aspect Ratio Utilities */
.aspect-golden {
  aspect-ratio: 1.618 / 1;
}

.aspect-photo {
  aspect-ratio: 4 / 3;
}

.aspect-video {
  aspect-ratio: 16 / 9;
}

/* Custom Clamp Utilities */
.text-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.text-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.text-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Custom Scroll Utilities */
.scroll-smooth {
  scroll-behavior: smooth;
}

.scroll-snap-x {
  scroll-snap-type: x mandatory;
}

.scroll-snap-y {
  scroll-snap-type: y mandatory;
}

.scroll-snap-start {
  scroll-snap-align: start;
}

.scroll-snap-center {
  scroll-snap-align: center;
}

/* Custom Focus Utilities */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
}

.focus-ring-inset {
  @apply focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500;
}

/* Custom Print Utilities */
.print-hidden {
  @media print {
    display: none !important;
  }
}

.print-visible {
  @media print {
    display: block !important;
  }
}

/* Custom Dark Mode Utilities */
@media (prefers-color-scheme: dark) {
  .dark-auto {
    @apply bg-gray-900 text-white;
  }
}

/* Custom Reduced Motion Utilities */
@media (prefers-reduced-motion: reduce) {
  .motion-reduce {
    animation: none !important;
    transition: none !important;
  }
}

/* Custom High Contrast Utilities */
@media (prefers-contrast: high) {
  .contrast-high {
    @apply border-2 border-black;
  }
}
