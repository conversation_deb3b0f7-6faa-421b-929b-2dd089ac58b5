{"featured": [{"id": "portfolio-website", "title": "Personal Portfolio Website", "description": "A modern, responsive portfolio website built with HTML5, Tailwind CSS, and Vanilla JavaScript. Features smooth animations, optimized performance, and comprehensive security measures.", "image": "assets/images/projects/portfolio-preview.jpg", "technologies": ["HTML5", "Tailwind CSS", "JavaScript", "Git"], "features": ["Responsive Design", "Smooth Scrolling", "Performance Optimized", "Security Enhanced", "Cross-browser Compatible"], "liveUrl": "https://syaamil.dev", "githubUrl": "https://github.com/syaamil/portfolio", "status": "Completed", "category": "Web Development", "duration": "2 weeks", "year": "2024"}, {"id": "ecommerce-landing", "title": "E-commerce Landing Page", "description": "A high-converting landing page for an e-commerce platform with modern design principles and optimized user experience.", "image": "assets/images/projects/ecommerce-preview.jpg", "technologies": ["HTML5", "CSS3", "JavaScript", "Tailwind CSS"], "features": ["Conversion Optimized", "Mobile First Design", "Fast Loading", "SEO Friendly", "Analytics Integration"], "liveUrl": "https://example-ecommerce.com", "githubUrl": "https://github.com/syaamil/ecommerce-landing", "status": "Completed", "category": "Web Development", "duration": "3 weeks", "year": "2024"}, {"id": "task-management-app", "title": "Task Management Application", "description": "A productivity application for managing tasks and projects with intuitive interface and powerful features.", "image": "assets/images/projects/taskapp-preview.jpg", "technologies": ["JavaScript", "HTML5", "CSS3", "Local Storage"], "features": ["Task Organization", "Progress Tracking", "Data Persistence", "Responsive Interface", "Keyboard Shortcuts"], "liveUrl": "https://taskapp-demo.com", "githubUrl": "https://github.com/syaamil/task-manager", "status": "Completed", "category": "Web Application", "duration": "4 weeks", "year": "2023"}], "other": [{"id": "weather-app", "title": "Weather Dashboard", "description": "A weather application with real-time data and beautiful visualizations.", "technologies": ["JavaScript", "API Integration", "CSS3"], "status": "Completed", "category": "Web Application", "year": "2023"}, {"id": "calculator-app", "title": "Scientific Calculator", "description": "A fully functional scientific calculator with advanced mathematical operations.", "technologies": ["JavaScript", "HTML5", "CSS3"], "status": "Completed", "category": "Web Application", "year": "2023"}, {"id": "blog-template", "title": "Blog Template", "description": "A clean and modern blog template with responsive design.", "technologies": ["HTML5", "CSS3", "JavaScript"], "status": "Completed", "category": "Template", "year": "2023"}], "categories": ["Web Development", "Web Application", "Template", "Mobile App", "API Development"], "technologies": ["HTML5", "CSS3", "JavaScript", "Tailwind CSS", "Node.js", "Git", "GitHub Pages"]}