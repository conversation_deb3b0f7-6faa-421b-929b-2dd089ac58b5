{"technical": [{"category": "Frontend Development", "skills": [{"name": "HTML5", "level": 95, "icon": "assets/icons/tech/html5.svg", "description": "Semantic markup and modern HTML5 features"}, {"name": "CSS3", "level": 90, "icon": "assets/icons/tech/css3.svg", "description": "Advanced styling, animations, and responsive design"}, {"name": "Tailwind CSS", "level": 92, "icon": "assets/icons/tech/tailwindcss.svg", "description": "Utility-first CSS framework for rapid development"}, {"name": "JavaScript", "level": 88, "icon": "assets/icons/tech/javascript.svg", "description": "Modern ES6+ features and DOM manipulation"}]}, {"category": "Backend Development", "skills": [{"name": "Node.js", "level": 85, "icon": "assets/icons/tech/nodejs.svg", "description": "Server-side JavaScript runtime environment"}]}, {"category": "Tools & Workflow", "skills": [{"name": "Git", "level": 87, "icon": "assets/icons/tech/git.svg", "description": "Version control and collaborative development"}, {"name": "GitHub Pages", "level": 90, "icon": "assets/icons/tech/githubpages.svg", "description": "Static site hosting and deployment"}]}], "soft": [{"name": "Problem Solving", "level": 92, "description": "Analytical thinking and creative solution development"}, {"name": "Team Collaboration", "level": 88, "description": "Effective communication and teamwork skills"}, {"name": "Project Management", "level": 85, "description": "Planning, organizing, and delivering projects on time"}, {"name": "Continuous Learning", "level": 95, "description": "Staying updated with latest technologies and trends"}, {"name": "Attention to Detail", "level": 90, "description": "Precision in code quality and user experience"}, {"name": "Creative Thinking", "level": 87, "description": "Innovative approaches to design and development"}], "expertise": [{"area": "Responsive Web Design", "description": "Creating websites that work seamlessly across all devices and screen sizes"}, {"area": "Performance Optimization", "description": "Optimizing websites for speed, SEO, and user experience"}, {"area": "User Experience (UX)", "description": "Designing intuitive and user-friendly interfaces"}, {"area": "Cross-browser Compatibility", "description": "Ensuring consistent functionality across different browsers"}, {"area": "Web Accessibility", "description": "Making websites accessible to users with disabilities"}, {"area": "Modern Web Standards", "description": "Following best practices and current web development standards"}]}