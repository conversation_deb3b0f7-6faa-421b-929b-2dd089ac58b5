/**
 * Component Management for Personal Portfolio
 * Author: <PERSON> Framework: Vanilla JavaScript
 * Description: Dynamic component rendering and management
 */

/**
 * Component Registry
 * Stores all component definitions and instances
 */
const ComponentRegistry = {
    components: {},
    instances: {},
    
    /**
     * Register a new component
     * @param {string} name - Component name
     * @param {Function} component - Component function
     */
    register(name, component) {
        this.components[name] = component;
    },
    
    /**
     * Create component instance
     * @param {string} name - Component name
     * @param {Object} props - Component properties
     * @param {Element} container - Container element
     * @returns {Object} - Component instance
     */
    create(name, props = {}, container = null) {
        if (!this.components[name]) {
            throw new Error(`Component '${name}' not found`);
        }
        
        const instance = this.components[name](props, container);
        const id = Utils.generateId(name);
        this.instances[id] = instance;
        
        return { id, instance };
    }
};

/**
 * Navigation Component
 * Handles navigation menu functionality
 */
function NavigationComponent(props, container) {
    const { social } = props;
    
    const render = () => {
        const nav = Utils.createElement('nav', {
            className: 'fixed top-0 left-0 right-0 bg-white/90 backdrop-blur-md z-50 border-b border-gray-200'
        });
        
        const navContent = `
            <div class="container-custom">
                <div class="flex-between py-4">
                    <div class="flex items-center space-x-2">
                        <div class="w-10 h-10 bg-gradient-primary rounded-lg flex-center">
                            <span class="text-white font-bold text-lg">SM</span>
                        </div>
                        <span class="font-semibold text-gray-900">Syaamil</span>
                    </div>
                    
                    <div class="hidden md:flex items-center space-x-8">
                        <a href="#home" class="nav-link">Home</a>
                        <a href="#about" class="nav-link">About</a>
                        <a href="#skills" class="nav-link">Skills</a>
                        <a href="#projects" class="nav-link">Projects</a>
                        <a href="#contact" class="nav-link">Contact</a>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <a href="${social.platforms.find(p => p.name === 'GitHub')?.url}" 
                           target="_blank" 
                           class="text-gray-600 hover:text-blue-600 transition-colors">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 0C5.37 0 0 5.37 0 12c0 5.31 3.435 9.795 8.205 11.385.6.105.825-.255.825-.57 0-.285-.015-1.23-.015-2.235-3.015.555-3.795-.735-4.035-1.41-.135-.345-.72-1.41-1.23-1.695-.42-.225-1.02-.78-.015-.795.945-.015 1.62.87 1.845 1.23 1.08 1.815 2.805 1.305 3.495.99.105-.78.42-1.305.765-1.605-2.67-.3-5.46-1.335-5.46-5.925 0-1.305.465-2.385 1.23-3.225-.12-.3-.54-1.53.12-3.18 0 0 1.005-.315 3.3 1.23.96-.27 1.98-.405 3-.405s2.04.135 3 .405c2.295-1.56 3.3-1.23 3.3-1.23.66 1.65.24 2.88.12 3.18.765.84 1.23 1.905 1.23 3.225 0 4.605-2.805 5.625-5.475 5.925.435.375.81 1.095.81 2.22 0 1.605-.015 2.895-.015 3.3 0 .315.225.69.825.57A12.02 12.02 0 0024 12c0-6.63-5.37-12-12-12z"/>
                            </svg>
                        </a>
                        
                        <button class="md:hidden text-gray-600 hover:text-blue-600" id="mobile-menu-btn">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                            </svg>
                        </button>
                    </div>
                </div>
                
                <!-- Mobile Menu -->
                <div class="md:hidden hidden" id="mobile-menu">
                    <div class="py-4 space-y-4">
                        <a href="#home" class="block nav-link">Home</a>
                        <a href="#about" class="block nav-link">About</a>
                        <a href="#skills" class="block nav-link">Skills</a>
                        <a href="#projects" class="block nav-link">Projects</a>
                        <a href="#contact" class="block nav-link">Contact</a>
                    </div>
                </div>
            </div>
        `;
        
        nav.innerHTML = navContent;
        
        // Setup mobile menu toggle
        const mobileMenuBtn = nav.querySelector('#mobile-menu-btn');
        const mobileMenu = nav.querySelector('#mobile-menu');
        
        mobileMenuBtn.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });
        
        // Close mobile menu when clicking on links
        mobileMenu.addEventListener('click', (e) => {
            if (e.target.matches('a')) {
                mobileMenu.classList.add('hidden');
            }
        });
        
        if (container) {
            container.appendChild(nav);
        }
        
        return nav;
    };
    
    return { render };
}

/**
 * Hero Section Component
 * Renders the main hero section
 */
function HeroComponent(props, container) {
    const { profile } = props;
    
    const render = () => {
        const hero = Utils.createElement('section', {
            id: 'home',
            className: 'min-h-screen flex-center bg-gradient-to-br from-blue-50 to-purple-50'
        });
        
        const heroContent = `
            <div class="container-custom text-center">
                <div class="max-w-4xl mx-auto">
                    <div class="mb-8">
                        <img src="${profile.personal.profileImage}" 
                             alt="${profile.personal.name}"
                             class="w-32 h-32 rounded-full mx-auto mb-6 shadow-lg object-cover">
                    </div>
                    
                    <h1 class="hero-title mb-6 fade-in">
                        ${profile.personal.name}
                    </h1>
                    
                    <p class="hero-subtitle mb-8 fade-in">
                        ${profile.personal.subtitle}
                    </p>
                    
                    <p class="text-lg text-gray-600 mb-12 max-w-2xl mx-auto fade-in">
                        ${profile.personal.description}
                    </p>
                    
                    <div class="flex flex-col sm:flex-row gap-4 justify-center fade-in">
                        <a href="#projects" class="btn-primary">
                            View My Work
                        </a>
                        <a href="#contact" class="btn-secondary">
                            Get In Touch
                        </a>
                    </div>
                </div>
                
                <!-- Scroll Indicator -->
                <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
                    <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"/>
                    </svg>
                </div>
            </div>
        `;
        
        hero.innerHTML = heroContent;
        
        if (container) {
            container.appendChild(hero);
        }
        
        return hero;
    };
    
    return { render };
}

/**
 * Skills Section Component
 * Renders skills with progress bars
 */
function SkillsComponent(props, container) {
    const { skills } = props;
    
    const render = () => {
        const skillsSection = Utils.createElement('section', {
            id: 'skills',
            className: 'section-container bg-white'
        });
        
        let skillsContent = `
            <div class="text-center mb-16">
                <h2 class="section-title">Skills & Expertise</h2>
                <p class="section-subtitle">
                    Technologies and tools I work with to bring ideas to life
                </p>
            </div>
            
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        `;
        
        // Render technical skills
        skills.technical.forEach(category => {
            skillsContent += `
                <div class="card-skill">
                    <h3 class="text-xl font-semibold mb-6 text-gray-900">${category.category}</h3>
                    <div class="space-y-4">
            `;
            
            category.skills.forEach(skill => {
                skillsContent += `
                    <div class="skill-item">
                        <div class="flex items-center justify-between mb-2">
                            <div class="flex items-center space-x-3">
                                <img src="${skill.icon}" alt="${skill.name}" class="w-6 h-6">
                                <span class="font-medium text-gray-900">${skill.name}</span>
                            </div>
                            <span class="text-sm text-gray-600">${skill.level}%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-gradient-primary h-2 rounded-full transition-all duration-1000" 
                                 style="width: 0%" 
                                 data-width="${skill.level}%"></div>
                        </div>
                        <p class="text-sm text-gray-600 mt-2">${skill.description}</p>
                    </div>
                `;
            });
            
            skillsContent += `
                    </div>
                </div>
            `;
        });
        
        skillsContent += `
            </div>
            
            <!-- Soft Skills -->
            <div class="mt-16">
                <h3 class="text-2xl font-bold text-center mb-8 text-gray-900">Soft Skills</h3>
                <div class="grid sm:grid-cols-2 lg:grid-cols-3 gap-6">
        `;
        
        skills.soft.forEach(skill => {
            skillsContent += `
                <div class="text-center p-6 rounded-lg bg-gray-50 hover:bg-blue-50 transition-colors">
                    <h4 class="font-semibold text-gray-900 mb-2">${skill.name}</h4>
                    <div class="w-full bg-gray-200 rounded-full h-2 mb-3">
                        <div class="bg-gradient-primary h-2 rounded-full transition-all duration-1000" 
                             style="width: 0%" 
                             data-width="${skill.level}%"></div>
                    </div>
                    <p class="text-sm text-gray-600">${skill.description}</p>
                </div>
            `;
        });
        
        skillsContent += `
                </div>
            </div>
        `;
        
        skillsSection.innerHTML = skillsContent;
        
        // Animate progress bars when in view
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const progressBars = entry.target.querySelectorAll('[data-width]');
                    progressBars.forEach(bar => {
                        setTimeout(() => {
                            bar.style.width = bar.dataset.width;
                        }, 300);
                    });
                    observer.unobserve(entry.target);
                }
            });
        });
        
        observer.observe(skillsSection);
        
        if (container) {
            container.appendChild(skillsSection);
        }
        
        return skillsSection;
    };
    
    return { render };
}

/**
 * Initialize all components
 * @param {Object} data - Application data
 */
async function initComponents(data) {
    try {
        // Register components
        ComponentRegistry.register('navigation', NavigationComponent);
        ComponentRegistry.register('hero', HeroComponent);
        ComponentRegistry.register('skills', SkillsComponent);
        
        // Get main container
        const main = document.querySelector('main') || document.body;
        
        // Create and render components
        const nav = ComponentRegistry.create('navigation', data, document.body);
        const hero = ComponentRegistry.create('hero', data, main);
        const skills = ComponentRegistry.create('skills', data, main);
        
        // Render all components
        nav.instance.render();
        hero.instance.render();
        skills.instance.render();
        
        console.log('All components initialized successfully');
        
    } catch (error) {
        console.error('Failed to initialize components:', error);
        throw error;
    }
}

/**
 * Export component system
 */
window.Components = {
    ComponentRegistry,
    initComponents,
    NavigationComponent,
    HeroComponent,
    SkillsComponent
};
