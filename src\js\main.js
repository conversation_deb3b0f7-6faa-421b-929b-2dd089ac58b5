/**
 * Main JavaScript File for Personal Portfolio
 * Author: <PERSON>
 * Framework: Vanilla JavaScript
 * Description: Core functionality and initialization
 */

// Note: Import statements will be handled by build process
// Utility functions and components will be loaded directly

/**
 * Application Configuration
 */
const CONFIG = {
  // Performance settings
  SCROLL_THROTTLE: 16, // 60fps
  RESIZE_DEBOUNCE: 250,
  LOADING_MIN_TIME: 500,

  // Security settings
  DISABLE_RIGHT_CLICK: true,
  DISABLE_INSPECT: true,
  DISABLE_COPY_PASTE: true,

  // Animation settings
  SMOOTH_SCROLL_DURATION: 800,
  INTERSECTION_THRESHOLD: 0.1,

  // Data sources
  DATA_SOURCES: {
    profile: "src/data/profile.json",
    skills: "src/data/skills.json",
    projects: "src/data/projects.json",
    social: "src/data/social.json",
  },
};

/**
 * Application State
 */
const AppState = {
  isLoading: true,
  currentSection: "home",
  data: {},
  components: {},
  observers: {},
};

/**
 * Security Implementation
 * Implements comprehensive security measures as requested
 */
class SecurityManager {
  static init() {
    if (CONFIG.DISABLE_RIGHT_CLICK) {
      this.disableRightClick();
    }

    if (CONFIG.DISABLE_INSPECT) {
      this.disableInspect();
    }

    if (CONFIG.DISABLE_COPY_PASTE) {
      this.disableCopyPaste();
    }

    this.preventKeyboardShortcuts();
    this.obfuscateContent();
  }

  /**
   * Disable right-click context menu
   */
  static disableRightClick() {
    document.addEventListener("contextmenu", (e) => {
      e.preventDefault();
      return false;
    });

    // Disable drag and drop
    document.addEventListener("dragstart", (e) => {
      e.preventDefault();
      return false;
    });
  }

  /**
   * Disable inspect element and developer tools
   */
  static disableInspect() {
    // Disable F12, Ctrl+Shift+I, Ctrl+Shift+J, Ctrl+U
    document.addEventListener("keydown", (e) => {
      if (
        e.key === "F12" ||
        (e.ctrlKey && e.shiftKey && e.key === "I") ||
        (e.ctrlKey && e.shiftKey && e.key === "J") ||
        (e.ctrlKey && e.key === "U") ||
        (e.ctrlKey && e.shiftKey && e.key === "C")
      ) {
        e.preventDefault();
        return false;
      }
    });

    // Detect developer tools
    let devtools = {
      open: false,
      orientation: null,
    };

    setInterval(() => {
      if (
        window.outerHeight - window.innerHeight > 200 ||
        window.outerWidth - window.innerWidth > 200
      ) {
        if (!devtools.open) {
          devtools.open = true;
          // Redirect or show warning
          document.body.innerHTML =
            '<div class="fixed inset-0 bg-black text-white flex items-center justify-center text-2xl">Access Denied</div>';
        }
      }
    }, 500);
  }

  /**
   * Disable copy-paste functionality
   */
  static disableCopyPaste() {
    // Disable text selection
    document.body.style.userSelect = "none";
    document.body.style.webkitUserSelect = "none";
    document.body.style.mozUserSelect = "none";
    document.body.style.msUserSelect = "none";

    // Disable copy-paste keyboard shortcuts
    document.addEventListener("keydown", (e) => {
      if (
        (e.ctrlKey && e.key === "c") ||
        (e.ctrlKey && e.key === "v") ||
        (e.ctrlKey && e.key === "x") ||
        (e.ctrlKey && e.key === "a") ||
        (e.ctrlKey && e.key === "s")
      ) {
        e.preventDefault();
        return false;
      }
    });

    // Disable copy event
    document.addEventListener("copy", (e) => {
      e.preventDefault();
      return false;
    });

    // Disable paste event
    document.addEventListener("paste", (e) => {
      e.preventDefault();
      return false;
    });
  }

  /**
   * Prevent common keyboard shortcuts
   */
  static preventKeyboardShortcuts() {
    document.addEventListener("keydown", (e) => {
      // Disable Ctrl+Shift+K (Firefox console)
      if (e.ctrlKey && e.shiftKey && e.key === "K") {
        e.preventDefault();
        return false;
      }

      // Disable Ctrl+Shift+E (Firefox network)
      if (e.ctrlKey && e.shiftKey && e.key === "E") {
        e.preventDefault();
        return false;
      }
    });
  }

  /**
   * Obfuscate content to prevent easy scraping
   */
  static obfuscateContent() {
    // Add invisible characters to text content
    const textNodes = document.createTreeWalker(
      document.body,
      NodeFilter.SHOW_TEXT,
      null,
      false
    );

    let node;
    while ((node = textNodes.nextNode())) {
      if (node.textContent.trim()) {
        // Add zero-width characters randomly
        const text = node.textContent;
        const obfuscated = text
          .split("")
          .map((char) => {
            return Math.random() > 0.8 ? char + "\u200B" : char;
          })
          .join("");
        node.textContent = obfuscated;
      }
    }
  }
}

/**
 * Loading Manager
 * Handles loading states and transitions
 */
class LoadingManager {
  static show() {
    const loader = document.createElement("div");
    loader.id = "app-loader";
    loader.className = "loading-overlay";
    loader.innerHTML = `
            <div class="text-center">
                <div class="loading-spinner mx-auto mb-4"></div>
                <p class="text-gray-600">Loading Portfolio...</p>
            </div>
        `;
    document.body.appendChild(loader);
  }

  static hide() {
    const loader = document.getElementById("app-loader");
    if (loader) {
      loader.style.opacity = "0";
      setTimeout(() => {
        loader.remove();
        AppState.isLoading = false;
      }, 300);
    }
  }
}

/**
 * Data Manager
 * Handles loading and caching of JSON data
 */
class DataManager {
  static async loadAllData() {
    try {
      const promises = Object.entries(CONFIG.DATA_SOURCES).map(
        async ([key, url]) => {
          const data = await loadJSON(url);
          return [key, data];
        }
      );

      const results = await Promise.all(promises);
      results.forEach(([key, data]) => {
        AppState.data[key] = data;
      });

      return AppState.data;
    } catch (error) {
      console.error("Failed to load data:", error);
      throw error;
    }
  }
}

/**
 * Application Initialization
 */
class App {
  static async init() {
    try {
      // Show loading screen
      LoadingManager.show();

      // Initialize security measures
      SecurityManager.init();

      // Load all data
      await DataManager.loadAllData();

      // Initialize components
      await initComponents(AppState.data);

      // Setup event listeners
      this.setupEventListeners();

      // Initialize intersection observers
      this.initIntersectionObservers();

      // Hide loading screen after minimum time
      setTimeout(() => {
        LoadingManager.hide();
      }, CONFIG.LOADING_MIN_TIME);
    } catch (error) {
      console.error("Application initialization failed:", error);
      LoadingManager.hide();
    }
  }

  /**
   * Setup global event listeners
   */
  static setupEventListeners() {
    // Smooth scroll for navigation links
    document.addEventListener("click", (e) => {
      if (e.target.matches('a[href^="#"]')) {
        e.preventDefault();
        const target = document.querySelector(e.target.getAttribute("href"));
        if (target) {
          target.scrollIntoView({
            behavior: "smooth",
            block: "start",
          });
        }
      }
    });

    // Throttled scroll handler
    window.addEventListener(
      "scroll",
      throttle(() => {
        this.handleScroll();
      }, CONFIG.SCROLL_THROTTLE)
    );

    // Debounced resize handler
    window.addEventListener(
      "resize",
      debounce(() => {
        this.handleResize();
      }, CONFIG.RESIZE_DEBOUNCE)
    );
  }

  /**
   * Handle scroll events
   */
  static handleScroll() {
    // Update active navigation
    const sections = document.querySelectorAll("section[id]");
    const scrollPos = window.scrollY + 100;

    sections.forEach((section) => {
      const top = section.offsetTop;
      const height = section.offsetHeight;
      const id = section.getAttribute("id");

      if (scrollPos >= top && scrollPos < top + height) {
        AppState.currentSection = id;
        this.updateActiveNavigation(id);
      }
    });
  }

  /**
   * Handle resize events
   */
  static handleResize() {
    // Recalculate layouts if needed
    // This can be extended based on specific needs
  }

  /**
   * Update active navigation item
   */
  static updateActiveNavigation(activeId) {
    document.querySelectorAll(".nav-link").forEach((link) => {
      link.classList.remove("active");
    });

    const activeLink = document.querySelector(`a[href="#${activeId}"]`);
    if (activeLink) {
      activeLink.classList.add("active");
    }
  }

  /**
   * Initialize intersection observers for animations
   */
  static initIntersectionObservers() {
    const observerOptions = {
      threshold: CONFIG.INTERSECTION_THRESHOLD,
      rootMargin: "0px 0px -50px 0px",
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("fade-in");
        }
      });
    }, observerOptions);

    // Observe all sections and cards
    document.querySelectorAll("section, .card").forEach((el) => {
      observer.observe(el);
    });

    AppState.observers.main = observer;
  }
}

/**
 * Initialize application when DOM is ready
 */
document.addEventListener("DOMContentLoaded", () => {
  App.init();
});

/**
 * Export for testing purposes
 */
export { App, SecurityManager, LoadingManager, DataManager, CONFIG, AppState };
