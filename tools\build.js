#!/usr/bin/env node

/**
 * Production Build Script for Personal Portfolio
 * Author: <PERSON>
 * Description: Optimized production build with comprehensive optimization
 */

import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import { spawn } from "child_process";
import { promisify } from "util";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);
const mkdir = promisify(fs.mkdir);
const copyFile = promisify(fs.copyFile);
const stat = promisify(fs.stat);
const readdir = promisify(fs.readdir);

// Configuration
const CONFIG = {
  root: path.resolve(__dirname, ".."),
  src: path.resolve(__dirname, "../src"),
  dist: path.resolve(__dirname, "../dist"),
  assets: path.resolve(__dirname, "../assets"),
  minify: {
    html: true,
    css: true,
    js: true,
  },
  optimization: {
    images: true,
    fonts: true,
    gzip: true,
  },
};

/**
 * Build Manager Class
 */
class BuildManager {
  constructor(config) {
    this.config = config;
    this.startTime = Date.now();
    this.stats = {
      files: 0,
      size: 0,
      optimized: 0,
    };
  }

  /**
   * Run the complete build process
   */
  async build() {
    try {
      console.log("🏗️  Starting production build...");
      console.log("📁 Source:", this.config.src);
      console.log("📦 Output:", this.config.dist);

      // Clean dist directory
      await this.cleanDist();

      // Create dist directories
      await this.createDirectories();

      // Build CSS
      await this.buildCSS();

      // Build JavaScript
      await this.buildJavaScript();

      // Process HTML
      await this.processHTML();

      // Copy and optimize assets
      await this.copyAssets();

      // Generate additional files
      await this.generateAdditionalFiles();

      // Show build summary
      this.showSummary();
    } catch (error) {
      console.error("❌ Build failed:", error);
      process.exit(1);
    }
  }

  /**
   * Clean the dist directory
   */
  async cleanDist() {
    console.log("🧹 Cleaning dist directory...");

    if (fs.existsSync(this.config.dist)) {
      await this.removeDirectory(this.config.dist);
    }
  }

  /**
   * Create necessary directories
   */
  async createDirectories() {
    console.log("📁 Creating directories...");

    const dirs = [
      this.config.dist,
      path.join(this.config.dist, "css"),
      path.join(this.config.dist, "js"),
      path.join(this.config.dist, "assets"),
      path.join(this.config.dist, "assets", "icons"),
      path.join(this.config.dist, "assets", "images"),
    ];

    for (const dir of dirs) {
      await mkdir(dir, { recursive: true });
    }
  }

  /**
   * Build CSS with Tailwind
   */
  async buildCSS() {
    console.log("🎨 Building CSS...");

    return new Promise((resolve, reject) => {
      const args = [
        "tailwindcss",
        "-i",
        "./src/css/input.css",
        "-o",
        "./dist/css/output.css",
        "--config",
        "./config/tailwind.config.js",
        "--minify",
      ];

      const process = spawn("npx", args, {
        cwd: this.config.root,
        stdio: "pipe",
      });

      let output = "";
      let error = "";

      process.stdout.on("data", (data) => {
        output += data.toString();
      });

      process.stderr.on("data", (data) => {
        error += data.toString();
      });

      process.on("close", (code) => {
        if (code === 0) {
          console.log("✅ CSS built successfully");
          this.stats.files++;
          resolve();
        } else {
          console.error("❌ CSS build failed:", error);
          reject(new Error(`CSS build failed with code ${code}`));
        }
      });
    });
  }

  /**
   * Build and minify JavaScript
   */
  async buildJavaScript() {
    console.log("⚡ Building JavaScript...");

    const jsFiles = [
      { src: "src/js/utils.js", dest: "dist/js/utils.js" },
      { src: "src/js/components.js", dest: "dist/js/components.js" },
      { src: "src/js/main.js", dest: "dist/js/main.js" },
    ];

    for (const file of jsFiles) {
      const srcPath = path.join(this.config.root, file.src);
      const destPath = path.join(this.config.root, file.dest);

      if (fs.existsSync(srcPath)) {
        let content = await readFile(srcPath, "utf8");

        if (this.config.minify.js) {
          content = this.minifyJavaScript(content);
        }

        await writeFile(destPath, content);
        this.stats.files++;
        console.log(`✅ Built ${file.dest}`);
      }
    }
  }

  /**
   * Process and optimize HTML
   */
  async processHTML() {
    console.log("📄 Processing HTML...");

    const htmlPath = path.join(this.config.root, "index.html");
    const distHtmlPath = path.join(this.config.dist, "index.html");

    if (fs.existsSync(htmlPath)) {
      let html = await readFile(htmlPath, "utf8");

      // Update asset paths for production
      html = this.updateAssetPaths(html);

      // Inject meta tags and optimizations
      html = this.injectOptimizations(html);

      // Minify HTML if enabled
      if (this.config.minify.html) {
        html = this.minifyHTML(html);
      }

      await writeFile(distHtmlPath, html);
      this.stats.files++;
      console.log("✅ HTML processed successfully");
    }
  }

  /**
   * Copy and optimize assets
   */
  async copyAssets() {
    console.log("📋 Copying assets...");

    if (fs.existsSync(this.config.assets)) {
      await this.copyDirectory(
        this.config.assets,
        path.join(this.config.dist, "assets")
      );
    }
  }

  /**
   * Generate additional files
   */
  async generateAdditionalFiles() {
    console.log("📝 Generating additional files...");

    // Generate robots.txt
    await this.generateRobotsTxt();

    // Generate sitemap.xml
    await this.generateSitemap();

    // Generate manifest.json
    await this.generateManifest();
  }

  /**
   * Update asset paths for production
   */
  updateAssetPaths(html) {
    // Update relative paths to work with the build structure
    return html
      .replace(/src="src\//g, 'src="')
      .replace(/href="src\//g, 'href="')
      .replace(/url\(src\//g, "url(");
  }

  /**
   * Inject performance and SEO optimizations
   */
  injectOptimizations(html) {
    // Add meta tags for performance and SEO
    const metaTags = `
    <meta name="description" content="Personal portfolio of Muhammad Syaamil Muzhaffar, Full Stack Developer">
    <meta name="keywords" content="Muhammad Syaamil Muzhaffar, Full Stack Developer, Web Developer, HTML5, CSS3, JavaScript, Tailwind CSS">
    <meta name="author" content="Muhammad Syaamil Muzhaffar">
    <meta name="robots" content="index, follow">
    <meta property="og:title" content="Muhammad Syaamil Muzhaffar - Full Stack Developer">
    <meta property="og:description" content="Personal portfolio showcasing modern web development projects and skills">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://syaamil.dev">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Muhammad Syaamil Muzhaffar - Full Stack Developer">
    <meta name="twitter:description" content="Personal portfolio showcasing modern web development projects and skills">
    <link rel="canonical" href="https://syaamil.dev">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    `;

    // Inject before closing head tag
    html = html.replace("</head>", `${metaTags}</head>`);

    // Add structured data
    const structuredData = `
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Person",
      "name": "Muhammad Syaamil Muzhaffar",
      "jobTitle": "Full Stack Developer",
      "url": "https://syaamil.dev",
      "sameAs": [
        "https://github.com/syaamil",
        "https://instagram.com/syaamil.dev",
        "https://medium.com/@syaamil"
      ]
    }
    </script>
    `;

    // Inject before closing head tag
    html = html.replace("</head>", `${structuredData}</head>`);

    return html;
  }

  /**
   * Minify HTML
   */
  minifyHTML(html) {
    return html
      .replace(/\s+/g, " ")
      .replace(/>\s+</g, "><")
      .replace(/\s+>/g, ">")
      .replace(/<\s+/g, "<")
      .trim();
  }

  /**
   * Minify JavaScript (basic implementation)
   */
  minifyJavaScript(js) {
    return js
      .replace(/\/\*[\s\S]*?\*\//g, "") // Remove block comments
      .replace(/\/\/.*$/gm, "") // Remove line comments
      .replace(/\s+/g, " ") // Collapse whitespace
      .replace(/;\s*}/g, "}") // Remove semicolons before closing braces
      .trim();
  }

  /**
   * Generate robots.txt
   */
  async generateRobotsTxt() {
    const robotsTxt = `User-agent: *
Allow: /

Sitemap: https://syaamil.dev/sitemap.xml`;

    await writeFile(path.join(this.config.dist, "robots.txt"), robotsTxt);
    console.log("✅ Generated robots.txt");
  }

  /**
   * Generate sitemap.xml
   */
  async generateSitemap() {
    const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>https://syaamil.dev/</loc>
    <lastmod>${new Date().toISOString().split("T")[0]}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>1.0</priority>
  </url>
</urlset>`;

    await writeFile(path.join(this.config.dist, "sitemap.xml"), sitemap);
    console.log("✅ Generated sitemap.xml");
  }

  /**
   * Generate manifest.json
   */
  async generateManifest() {
    const manifest = {
      name: "Muhammad Syaamil Muzhaffar Portfolio",
      short_name: "Syaamil Portfolio",
      description: "Personal portfolio of Muhammad Syaamil Muzhaffar",
      start_url: "/",
      display: "standalone",
      background_color: "#ffffff",
      theme_color: "#3b82f6",
      icons: [
        {
          src: "/assets/icons/icon-192.png",
          sizes: "192x192",
          type: "image/png",
        },
        {
          src: "/assets/icons/icon-512.png",
          sizes: "512x512",
          type: "image/png",
        },
      ],
    };

    await writeFile(
      path.join(this.config.dist, "manifest.json"),
      JSON.stringify(manifest, null, 2)
    );
    console.log("✅ Generated manifest.json");
  }

  /**
   * Copy directory recursively
   */
  async copyDirectory(src, dest) {
    await mkdir(dest, { recursive: true });

    const entries = await readdir(src, { withFileTypes: true });

    for (const entry of entries) {
      const srcPath = path.join(src, entry.name);
      const destPath = path.join(dest, entry.name);

      if (entry.isDirectory()) {
        await this.copyDirectory(srcPath, destPath);
      } else {
        await copyFile(srcPath, destPath);
        this.stats.files++;

        const stats = await stat(srcPath);
        this.stats.size += stats.size;
      }
    }
  }

  /**
   * Remove directory recursively
   */
  async removeDirectory(dir) {
    if (fs.existsSync(dir)) {
      const entries = await readdir(dir, { withFileTypes: true });

      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);

        if (entry.isDirectory()) {
          await this.removeDirectory(fullPath);
        } else {
          fs.unlinkSync(fullPath);
        }
      }

      fs.rmdirSync(dir);
    }
  }

  /**
   * Show build summary
   */
  showSummary() {
    const duration = Date.now() - this.startTime;
    const sizeKB = (this.stats.size / 1024).toFixed(2);

    console.log("\n🎉 Build completed successfully!");
    console.log("📊 Build Summary:");
    console.log(`   ⏱️  Duration: ${duration}ms`);
    console.log(`   📁 Files: ${this.stats.files}`);
    console.log(`   📦 Total size: ${sizeKB} KB`);
    console.log(`   📍 Output: ${this.config.dist}`);
    console.log("\n✅ Ready for deployment!");
  }
}

/**
 * Run the build process
 */
async function runBuild() {
  const builder = new BuildManager(CONFIG);
  await builder.build();
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runBuild().catch(console.error);
}

export { BuildManager, runBuild };
