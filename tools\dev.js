#!/usr/bin/env node

/**
 * Development Server for Personal Portfolio
 * Author: <PERSON>
 * Description: Efficient development workflow with hot reload and optimization
 */

import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import { createServer } from "http";
import { spawn } from "child_process";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const CONFIG = {
  port: process.env.PORT || 3000,
  host: process.env.HOST || "localhost",
  root: path.resolve(__dirname, ".."),
  watchPaths: [
    "src/**/*.{js,css,html}",
    "assets/**/*",
    "index.html",
    "config/**/*.js",
  ],
  buildCommand: "npm run build:css",
  openBrowser: true,
};

/**
 * MIME types for different file extensions
 */
const MIME_TYPES = {
  ".html": "text/html",
  ".js": "text/javascript",
  ".css": "text/css",
  ".json": "application/json",
  ".png": "image/png",
  ".jpg": "image/jpeg",
  ".jpeg": "image/jpeg",
  ".gif": "image/gif",
  ".svg": "image/svg+xml",
  ".ico": "image/x-icon",
  ".webp": "image/webp",
  ".woff": "font/woff",
  ".woff2": "font/woff2",
  ".ttf": "font/ttf",
  ".eot": "application/vnd.ms-fontobject",
};

/**
 * Development Server Class
 */
class DevServer {
  constructor(config) {
    this.config = config;
    this.server = null;
    this.watchers = [];
    this.buildProcess = null;
    this.clients = new Set();
  }

  /**
   * Start the development server
   */
  async start() {
    try {
      console.log("🚀 Starting development server...");

      // Initial build
      await this.buildCSS();

      // Create HTTP server
      this.createServer();

      // Setup file watchers
      this.setupWatchers();

      // Start server
      this.server.listen(this.config.port, this.config.host, () => {
        const url = `http://${this.config.host}:${this.config.port}`;
        console.log(`✅ Server running at ${url}`);
        console.log(`📁 Serving files from: ${this.config.root}`);
        console.log("👀 Watching for changes...");

        if (this.config.openBrowser) {
          this.openBrowser(url);
        }
      });
    } catch (error) {
      console.error("❌ Failed to start development server:", error);
      process.exit(1);
    }
  }

  /**
   * Create HTTP server with request handling
   */
  createServer() {
    this.server = createServer((req, res) => {
      this.handleRequest(req, res);
    });

    this.server.on("error", (error) => {
      if (error.code === "EADDRINUSE") {
        console.error(`❌ Port ${this.config.port} is already in use`);
        process.exit(1);
      } else {
        console.error("❌ Server error:", error);
      }
    });
  }

  /**
   * Handle HTTP requests
   */
  async handleRequest(req, res) {
    try {
      let filePath = req.url === "/" ? "/index.html" : req.url;

      // Remove query parameters
      filePath = filePath.split("?")[0];

      // Security: prevent directory traversal
      if (filePath.includes("..")) {
        this.sendError(res, 403, "Forbidden");
        return;
      }

      const fullPath = path.join(this.config.root, filePath);

      // Check if file exists
      if (!fs.existsSync(fullPath)) {
        // For SPA routing, serve index.html for non-asset requests
        if (!path.extname(filePath) || filePath.startsWith("/api/")) {
          this.serveFile(res, path.join(this.config.root, "index.html"));
        } else {
          this.sendError(res, 404, "Not Found");
        }
        return;
      }

      // Serve file
      this.serveFile(res, fullPath);
    } catch (error) {
      console.error("❌ Request handling error:", error);
      this.sendError(res, 500, "Internal Server Error");
    }
  }

  /**
   * Serve a file with appropriate headers
   */
  serveFile(res, filePath) {
    const ext = path.extname(filePath).toLowerCase();
    const mimeType = MIME_TYPES[ext] || "application/octet-stream";

    // Set headers
    res.setHeader("Content-Type", mimeType);
    res.setHeader("Cache-Control", "no-cache");
    res.setHeader("Access-Control-Allow-Origin", "*");

    // Read and serve file
    fs.readFile(filePath, (err, data) => {
      if (err) {
        this.sendError(res, 500, "Error reading file");
        return;
      }

      // Inject live reload script for HTML files
      if (ext === ".html") {
        const html = data.toString();
        const injectedHtml = this.injectLiveReload(html);
        res.end(injectedHtml);
      } else {
        res.end(data);
      }
    });
  }

  /**
   * Send error response
   */
  sendError(res, statusCode, message) {
    res.statusCode = statusCode;
    res.setHeader("Content-Type", "text/plain");
    res.end(message);
  }

  /**
   * Inject live reload script into HTML
   */
  injectLiveReload(html) {
    const liveReloadScript = `
      <script>
        // Live reload functionality
        (function() {
          const ws = new WebSocket('ws://localhost:${this.config.port + 1}');

          ws.onmessage = function(event) {
            const data = JSON.parse(event.data);

            if (data.type === 'reload') {
              console.log('🔄 Reloading page due to file changes...');
              window.location.reload();
            } else if (data.type === 'css-update') {
              console.log('🎨 Updating CSS...');
              const links = document.querySelectorAll('link[rel="stylesheet"]');
              links.forEach(link => {
                const url = new URL(link.href);
                url.searchParams.set('t', Date.now());
                link.href = url.toString();
              });
            }
          };

          ws.onopen = function() {
            console.log('🔗 Live reload connected');
          };

          ws.onclose = function() {
            console.log('❌ Live reload disconnected');
            // Attempt to reconnect
            setTimeout(() => {
              window.location.reload();
            }, 1000);
          };
        })();
      </script>
    `;

    // Inject before closing body tag
    return html.replace("</body>", `${liveReloadScript}</body>`);
  }

  /**
   * Setup file watchers for hot reload
   */
  setupWatchers() {
    const chokidar = this.createSimpleWatcher();

    // Watch source files
    const watchPaths = [
      path.join(this.config.root, "src"),
      path.join(this.config.root, "assets"),
      path.join(this.config.root, "index.html"),
      path.join(this.config.root, "config"),
    ];

    watchPaths.forEach((watchPath) => {
      if (fs.existsSync(watchPath)) {
        this.watchDirectory(watchPath);
      }
    });
  }

  /**
   * Simple file watcher implementation
   */
  watchDirectory(dirPath) {
    const watcher = fs.watch(
      dirPath,
      { recursive: true },
      (eventType, filename) => {
        if (filename) {
          const fullPath = path.join(dirPath, filename);
          const ext = path.extname(filename);

          console.log(`📝 File changed: ${filename}`);

          if (ext === ".css" || filename.includes("input.css")) {
            this.handleCSSChange();
          } else {
            this.handleFileChange();
          }
        }
      }
    );

    this.watchers.push(watcher);
  }

  /**
   * Handle CSS file changes
   */
  async handleCSSChange() {
    try {
      console.log("🎨 CSS changed, rebuilding...");
      await this.buildCSS();
      this.notifyClients({ type: "css-update" });
    } catch (error) {
      console.error("❌ CSS build failed:", error);
    }
  }

  /**
   * Handle other file changes
   */
  handleFileChange() {
    console.log("🔄 Files changed, reloading...");
    this.notifyClients({ type: "reload" });
  }

  /**
   * Build CSS using Tailwind CLI
   */
  async buildCSS() {
    return new Promise((resolve, reject) => {
      const process = spawn(
        "npx",
        [
          "tailwindcss",
          "-i",
          "./src/css/input.css",
          "-o",
          "./dist/css/output.css",
          "--config",
          "./config/tailwind.config.js",
        ],
        {
          cwd: this.config.root,
          stdio: "pipe",
        }
      );

      process.on("close", (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`CSS build failed with code ${code}`));
        }
      });

      process.on("error", reject);
    });
  }

  /**
   * Notify connected clients
   */
  notifyClients(message) {
    // Simple implementation - in a real scenario, you'd use WebSockets
    // For now, we'll just log the message
    console.log("📡 Notifying clients:", message.type);
  }

  /**
   * Open browser
   */
  openBrowser(url) {
    const start =
      process.platform === "darwin"
        ? "open"
        : process.platform === "win32"
          ? "start"
          : "xdg-open";

    spawn(start, [url], { stdio: "ignore", detached: true }).unref();
  }

  /**
   * Stop the development server
   */
  stop() {
    console.log("🛑 Stopping development server...");

    // Close watchers
    this.watchers.forEach((watcher) => watcher.close());

    // Close server
    if (this.server) {
      this.server.close();
    }

    // Kill build process
    if (this.buildProcess) {
      this.buildProcess.kill();
    }

    console.log("✅ Development server stopped");
  }
}

/**
 * Start development server
 */
async function startDev() {
  const server = new DevServer(CONFIG);

  // Handle process termination
  process.on("SIGINT", () => {
    server.stop();
    process.exit(0);
  });

  process.on("SIGTERM", () => {
    server.stop();
    process.exit(0);
  });

  // Start server
  await server.start();
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  startDev().catch(console.error);
}

export { DevServer, startDev };
