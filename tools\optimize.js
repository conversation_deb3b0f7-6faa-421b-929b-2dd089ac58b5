#!/usr/bin/env node

/**
 * Asset Optimization Script for Personal Portfolio
 * Author: <PERSON>
 * Description: Comprehensive asset optimization for maximum performance
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { promisify } from 'util';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);
const readdir = promisify(fs.readdir);
const stat = promisify(fs.stat);

// Configuration
const CONFIG = {
  root: path.resolve(__dirname, '..'),
  dist: path.resolve(__dirname, '../dist'),
  assets: path.resolve(__dirname, '../assets'),
  optimization: {
    images: {
      quality: 85,
      progressive: true,
      removeMetadata: true
    },
    css: {
      removeUnused: true,
      minify: true,
      autoprefixer: true
    },
    js: {
      minify: true,
      removeComments: true,
      removeConsole: true
    },
    html: {
      minify: true,
      removeComments: true,
      collapseWhitespace: true
    }
  }
};

/**
 * Asset Optimizer Class
 */
class AssetOptimizer {
  constructor(config) {
    this.config = config;
    this.stats = {
      originalSize: 0,
      optimizedSize: 0,
      filesProcessed: 0,
      timeStart: Date.now()
    };
  }

  /**
   * Run complete optimization process
   */
  async optimize() {
    try {
      console.log('🚀 Starting asset optimization...');
      
      // Optimize images
      await this.optimizeImages();
      
      // Optimize CSS
      await this.optimizeCSS();
      
      // Optimize JavaScript
      await this.optimizeJavaScript();
      
      // Optimize HTML
      await this.optimizeHTML();
      
      // Generate performance report
      this.generateReport();
      
    } catch (error) {
      console.error('❌ Optimization failed:', error);
      process.exit(1);
    }
  }

  /**
   * Optimize images
   */
  async optimizeImages() {
    console.log('🖼️  Optimizing images...');
    
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.svg', '.webp'];
    const imagePaths = await this.findFilesByExtensions(this.config.dist, imageExtensions);
    
    for (const imagePath of imagePaths) {
      await this.optimizeImage(imagePath);
    }
    
    console.log(`✅ Optimized ${imagePaths.length} images`);
  }

  /**
   * Optimize individual image
   */
  async optimizeImage(imagePath) {
    try {
      const ext = path.extname(imagePath).toLowerCase();
      const originalStats = await stat(imagePath);
      this.stats.originalSize += originalStats.size;
      
      if (ext === '.svg') {
        await this.optimizeSVG(imagePath);
      } else {
        // For other image formats, we'll implement basic optimization
        await this.optimizeRasterImage(imagePath);
      }
      
      const optimizedStats = await stat(imagePath);
      this.stats.optimizedSize += optimizedStats.size;
      this.stats.filesProcessed++;
      
    } catch (error) {
      console.warn(`⚠️  Failed to optimize ${imagePath}:`, error.message);
    }
  }

  /**
   * Optimize SVG files
   */
  async optimizeSVG(svgPath) {
    const content = await readFile(svgPath, 'utf8');
    
    // Basic SVG optimization
    let optimized = content
      // Remove comments
      .replace(/<!--[\s\S]*?-->/g, '')
      // Remove unnecessary whitespace
      .replace(/\s+/g, ' ')
      // Remove empty attributes
      .replace(/\s*=\s*""\s*/g, '')
      // Remove default values
      .replace(/\s*fill="black"\s*/g, '')
      .replace(/\s*stroke="none"\s*/g, '')
      // Trim
      .trim();
    
    await writeFile(svgPath, optimized);
  }

  /**
   * Optimize raster images (placeholder implementation)
   */
  async optimizeRasterImage(imagePath) {
    // In a real implementation, you would use libraries like sharp or imagemin
    // For now, we'll just log that the image was processed
    console.log(`📸 Processed: ${path.basename(imagePath)}`);
  }

  /**
   * Optimize CSS files
   */
  async optimizeCSS() {
    console.log('🎨 Optimizing CSS...');
    
    const cssFiles = await this.findFilesByExtensions(this.config.dist, ['.css']);
    
    for (const cssFile of cssFiles) {
      await this.optimizeCSSFile(cssFile);
    }
    
    console.log(`✅ Optimized ${cssFiles.length} CSS files`);
  }

  /**
   * Optimize individual CSS file
   */
  async optimizeCSSFile(cssPath) {
    try {
      const originalStats = await stat(cssPath);
      this.stats.originalSize += originalStats.size;
      
      let content = await readFile(cssPath, 'utf8');
      
      // CSS optimization
      content = this.minifyCSS(content);
      
      await writeFile(cssPath, content);
      
      const optimizedStats = await stat(cssPath);
      this.stats.optimizedSize += optimizedStats.size;
      this.stats.filesProcessed++;
      
    } catch (error) {
      console.warn(`⚠️  Failed to optimize ${cssPath}:`, error.message);
    }
  }

  /**
   * Minify CSS content
   */
  minifyCSS(css) {
    return css
      // Remove comments
      .replace(/\/\*[\s\S]*?\*\//g, '')
      // Remove unnecessary whitespace
      .replace(/\s+/g, ' ')
      // Remove spaces around certain characters
      .replace(/\s*([{}:;,>+~])\s*/g, '$1')
      // Remove trailing semicolons
      .replace(/;}/g, '}')
      // Remove empty rules
      .replace(/[^{}]+{\s*}/g, '')
      // Trim
      .trim();
  }

  /**
   * Optimize JavaScript files
   */
  async optimizeJavaScript() {
    console.log('⚡ Optimizing JavaScript...');
    
    const jsFiles = await this.findFilesByExtensions(this.config.dist, ['.js']);
    
    for (const jsFile of jsFiles) {
      await this.optimizeJSFile(jsFile);
    }
    
    console.log(`✅ Optimized ${jsFiles.length} JavaScript files`);
  }

  /**
   * Optimize individual JavaScript file
   */
  async optimizeJSFile(jsPath) {
    try {
      const originalStats = await stat(jsPath);
      this.stats.originalSize += originalStats.size;
      
      let content = await readFile(jsPath, 'utf8');
      
      // JavaScript optimization
      content = this.minifyJavaScript(content);
      
      await writeFile(jsPath, content);
      
      const optimizedStats = await stat(jsPath);
      this.stats.optimizedSize += optimizedStats.size;
      this.stats.filesProcessed++;
      
    } catch (error) {
      console.warn(`⚠️  Failed to optimize ${jsPath}:`, error.message);
    }
  }

  /**
   * Minify JavaScript content
   */
  minifyJavaScript(js) {
    return js
      // Remove block comments
      .replace(/\/\*[\s\S]*?\*\//g, '')
      // Remove line comments (but preserve URLs)
      .replace(/(?:^|\s)\/\/.*$/gm, '')
      // Remove console.log statements
      .replace(/console\.log\([^)]*\);?/g, '')
      // Remove console.error statements (keep for production debugging)
      // .replace(/console\.error\([^)]*\);?/g, '')
      // Collapse whitespace
      .replace(/\s+/g, ' ')
      // Remove spaces around operators and punctuation
      .replace(/\s*([=+\-*/%<>!&|^~?:;,(){}[\]])\s*/g, '$1')
      // Remove unnecessary semicolons
      .replace(/;+/g, ';')
      .replace(/;}/g, '}')
      // Trim
      .trim();
  }

  /**
   * Optimize HTML files
   */
  async optimizeHTML() {
    console.log('📄 Optimizing HTML...');
    
    const htmlFiles = await this.findFilesByExtensions(this.config.dist, ['.html']);
    
    for (const htmlFile of htmlFiles) {
      await this.optimizeHTMLFile(htmlFile);
    }
    
    console.log(`✅ Optimized ${htmlFiles.length} HTML files`);
  }

  /**
   * Optimize individual HTML file
   */
  async optimizeHTMLFile(htmlPath) {
    try {
      const originalStats = await stat(htmlPath);
      this.stats.originalSize += originalStats.size;
      
      let content = await readFile(htmlPath, 'utf8');
      
      // HTML optimization
      content = this.minifyHTML(content);
      
      await writeFile(htmlPath, content);
      
      const optimizedStats = await stat(htmlPath);
      this.stats.optimizedSize += optimizedStats.size;
      this.stats.filesProcessed++;
      
    } catch (error) {
      console.warn(`⚠️  Failed to optimize ${htmlPath}:`, error.message);
    }
  }

  /**
   * Minify HTML content
   */
  minifyHTML(html) {
    return html
      // Remove HTML comments (but preserve conditional comments)
      .replace(/<!--(?!\s*(?:\[if [^\]]+]|<!|>))[\s\S]*?-->/g, '')
      // Collapse whitespace
      .replace(/\s+/g, ' ')
      // Remove spaces between tags
      .replace(/>\s+</g, '><')
      // Remove spaces around attributes
      .replace(/\s*=\s*/g, '=')
      // Remove quotes around single-word attribute values
      .replace(/=["']([a-zA-Z0-9\-_]+)["']/g, '=$1')
      // Remove trailing spaces
      .replace(/\s+>/g, '>')
      // Remove leading spaces
      .replace(/<\s+/g, '<')
      // Trim
      .trim();
  }

  /**
   * Find files by extensions recursively
   */
  async findFilesByExtensions(dir, extensions) {
    const files = [];
    
    if (!fs.existsSync(dir)) {
      return files;
    }
    
    const entries = await readdir(dir, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = path.join(dir, entry.name);
      
      if (entry.isDirectory()) {
        const subFiles = await this.findFilesByExtensions(fullPath, extensions);
        files.push(...subFiles);
      } else if (extensions.includes(path.extname(entry.name).toLowerCase())) {
        files.push(fullPath);
      }
    }
    
    return files;
  }

  /**
   * Generate optimization report
   */
  generateReport() {
    const duration = Date.now() - this.stats.timeStart;
    const originalSizeKB = (this.stats.originalSize / 1024).toFixed(2);
    const optimizedSizeKB = (this.stats.optimizedSize / 1024).toFixed(2);
    const savedKB = ((this.stats.originalSize - this.stats.optimizedSize) / 1024).toFixed(2);
    const savedPercent = ((1 - this.stats.optimizedSize / this.stats.originalSize) * 100).toFixed(1);
    
    console.log('\n🎉 Optimization completed!');
    console.log('📊 Optimization Report:');
    console.log(`   ⏱️  Duration: ${duration}ms`);
    console.log(`   📁 Files processed: ${this.stats.filesProcessed}`);
    console.log(`   📦 Original size: ${originalSizeKB} KB`);
    console.log(`   🗜️  Optimized size: ${optimizedSizeKB} KB`);
    console.log(`   💾 Space saved: ${savedKB} KB (${savedPercent}%)`);
    console.log('\n✅ Assets optimized for maximum performance!');
  }
}

/**
 * Run optimization process
 */
async function runOptimization() {
  const optimizer = new AssetOptimizer(CONFIG);
  await optimizer.optimize();
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runOptimization().catch(console.error);
}

export { AssetOptimizer, runOptimization };
